import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { css } from "@emotion/react";

import { Resource } from "@/common/types";

import { ResourceColumn } from "../../hooks/useResourceListColumns";
import ResourceTableRow from "./ResourceTableRow";

/**
 * {@link SortableResourceTableRow}のprops
 */
export interface SortableResourceTableRowProps {
  /** 何行目か */
  index: number;
  /** 表示するリソース */
  resource: Resource;
  /** Tableの表示幅。（タスクリストと同期させる） */
  listWidth: number;
  /** 行の高さ */
  rowHeight: number;
  /** 表の並び替えを行うエリアの幅 */
  gripAreaWidth: number;
  /** 表示する項目情報 */
  resourceColumns: ResourceColumn[];
}

/**
 * ResourceTableの一行をdnd-kitでソート可能としたもの
 */
const SortableResourceTableRow: React.FC<SortableResourceTableRowProps> = ({
  index,
  resource,
  listWidth,
  rowHeight,
  gripAreaWidth,
  resourceColumns,
}) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    setActivatorNodeRef,
    isDragging,
  } = useSortable({
    id: resource.id,
  });

  return (
    <div
      style={{
        transform: CSS.Translate.toString(transform),
        transition,
      }}
      ref={setNodeRef}
    >
      {isDragging ? (
        <div
          className="table-row text-ellipsis"
          css={css`
            height: ${rowHeight}px;
            max-width: ${listWidth}px;
            min-width: ${listWidth}px;
          `}
        ></div>
      ) : (
        <ResourceTableRow
          index={index}
          resource={resource}
          listWidth={listWidth}
          rowHeight={rowHeight}
          gripAreaWidth={gripAreaWidth}
          resourceColumns={resourceColumns}
          isDragging={isDragging}
          dragHandleProps={{ setActivatorNodeRef, attributes, listeners }}
        />
      )}
    </div>
  );
};

export default SortableResourceTableRow;
