import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

// Define the context interface for cross-window communication
interface WindowSyncContextType {
  registerWindow: (windowId: string, window: Window) => void;
  unregisterWindow: (windowId: string) => void;
  broadcastEvent: (event: string, data: any) => void;
  listenForEvent: (event: string, callback: (data: any) => void) => () => void;
  isInPopout: boolean;
}

// Create the context with default values
const WindowSyncContext = createContext<WindowSyncContextType>({
  registerWindow: () => {},
  unregisterWindow: () => {},
  broadcastEvent: () => {},
  listenForEvent: () => () => {},
  isInPopout: false,
});

// Type for props
interface WindowSyncProviderProps {
  children: ReactNode;
}

/**
 * Provider that handles synchronization between main window and popped-out windows
 */
export const WindowSyncProvider: React.FC<WindowSyncProviderProps> = ({ children }) => {
  const [popoutWindows, setPopoutWindows] = useState<Record<string, Window>>({});
  const [eventListeners, setEventListeners] = useState<Record<string, Array<(data: any) => void>>>({});
  // Detect if current window is a pop-out window
  const isInPopout = window.opener !== null && window.opener !== window;

  // Register a popped-out window for communication
  const registerWindow = (windowId: string, window: Window) => {
    setPopoutWindows(prev => ({ ...prev, [windowId]: window }));
  };

  // Unregister a window when it's closed
  const unregisterWindow = (windowId: string) => {
    setPopoutWindows(prev => {
      const newWindows = { ...prev };
      delete newWindows[windowId];
      return newWindows;
    });
  };

  // Broadcast an event to all registered windows
  const broadcastEvent = (event: string, data: any) => {
    // Send to this window's listeners
    const listeners = eventListeners[event] || [];
    listeners.forEach(listener => listener(data));

    // Send to popped-out windows
    Object.values(popoutWindows).forEach(win => {
      try {
        if (!win.closed) {
          win.postMessage({
            type: 'WINDOW_SYNC_EVENT',
            event,
            data,
          }, '*');
        }
      } catch (error) {
        console.error('Error sending message to popped-out window:', error);
      }
    });

    // If in a popped-out window, send to parent window
    if (isInPopout && window.opener) {
      try {
        window.opener.postMessage({
          type: 'WINDOW_SYNC_EVENT',
          event,
          data,
        }, '*');
      } catch (error) {
        console.error('Error sending message to parent window:', error);
      }
    }
  };

  // Register a listener for an event
  const listenForEvent = (event: string, callback: (data: any) => void) => {
    setEventListeners(prev => ({
      ...prev,
      [event]: [...(prev[event] || []), callback],
    }));

    // Return a function to unregister the listener
    return () => {
      setEventListeners(prev => ({
        ...prev,
        [event]: (prev[event] || []).filter(cb => cb !== callback),
      }));
    };
  };

  // Listen for messages from other windows
  useEffect(() => {
    const handleMessage = (event: MessageEvent) => {
      if (event.data?.type === 'WINDOW_SYNC_EVENT') {
        const { event: eventName, data } = event.data;
        const listeners = eventListeners[eventName] || [];
        listeners.forEach(listener => listener(data));
      }
    };

    window.addEventListener('message', handleMessage);
    return () => {
      window.removeEventListener('message', handleMessage);
    };
  }, [eventListeners]);

  return (
    <WindowSyncContext.Provider value={{
      registerWindow,
      unregisterWindow,
      broadcastEvent,
      listenForEvent,
      isInPopout,
    }}>
      {children}
    </WindowSyncContext.Provider>
  );
};

// Hook to use the WindowSync context
export const useWindowSync = () => useContext(WindowSyncContext);
