import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';

export type MonitorDisplayState = 'hidden' | 'inline' | 'popped-out';

// Type for popup configuration
export interface PopupConfig {
  title: string;
  width: number;
  height: number;
  features: string[];
  className: string;
}

export interface PopupWindowInfo extends PopupConfig {
  isOpen: boolean;
}

interface MonitorStoreState {
  resourceMonitorState: MonitorDisplayState;
  temporalResourceMonitorState: MonitorDisplayState;
  // Popup window management
  resourcePopup: PopupWindowInfo;
  temporalResourcePopup: PopupWindowInfo;
  
  setResourceMonitorState: (state: MonitorDisplayState) => void;
  setTemporalResourceMonitorState: (state: MonitorDisplayState) => void;
  // New method to initialize state from styling store
  initializeFromStylingStore: (showResource: boolean, showTemporal: boolean) => void;
  // Method to force reset both monitors to hidden state
  resetToHidden: () => void;
  
  // Popup management methods
  openResourcePopup: (config: PopupConfig) => void;
  closeResourcePopup: () => void;
  openTemporalResourcePopup: (config: PopupConfig) => void;
  closeTemporalResourcePopup: () => void;
}

export const useMonitorStore = create<MonitorStoreState>()(
  subscribeWithSelector((set, get) => ({
    resourceMonitorState: 'hidden',
    temporalResourceMonitorState: 'hidden',
    resourcePopup: {
      isOpen: false,
      title: '',
      width: 800,
      height: 600,
      features: [],
      className: '',
    },
    temporalResourcePopup: {
      isOpen: false,
      title: '',
      width: 800,
      height: 600,
      features: [],
      className: '',
    },
    
    setResourceMonitorState: (state) => set({ resourceMonitorState: state }),
    setTemporalResourceMonitorState: (state) => set({ temporalResourceMonitorState: state }),
    
    initializeFromStylingStore: (showResource: boolean, showTemporal: boolean) => {
      const currentResourceState = get().resourceMonitorState;
      const currentTemporalState = get().temporalResourceMonitorState;
      
      // Force monitors to hidden by default unless explicitly enabled
      // Only show inline if the styling store explicitly shows it should be visible
      if (showResource && currentResourceState === 'hidden') {
        set({ resourceMonitorState: 'inline' });
      } else if (!showResource) {
        // Always hide when styling store says it should be hidden, unless it's popped out
        if (currentResourceState !== 'popped-out') {
          set({ resourceMonitorState: 'hidden' });
        }
      }
      
      if (showTemporal && currentTemporalState === 'hidden') {
        set({ temporalResourceMonitorState: 'inline' });
      } else if (!showTemporal) {
        // Always hide when styling store says it should be hidden, unless it's popped out
        if (currentTemporalState !== 'popped-out') {
          set({ temporalResourceMonitorState: 'hidden' });
        }
      }
    },
    
    // Popup management
    openResourcePopup: (config: PopupConfig) => set(() => ({
      resourceMonitorState: 'popped-out',
      resourcePopup: {
        isOpen: true,
        title: config.title,
        width: config.width,
        height: config.height,
        features: config.features,
        className: config.className,
      },
    })),
    
    closeResourcePopup: () => set((state) => ({
      resourceMonitorState: 'hidden', // Changed from 'inline' to 'hidden' to default to off
      resourcePopup: {
        ...state.resourcePopup,
        isOpen: false,
      },
    })),
    
    openTemporalResourcePopup: (config: PopupConfig) => set(() => ({
      temporalResourceMonitorState: 'popped-out',
      temporalResourcePopup: {
        isOpen: true,
        title: config.title,
        width: config.width,
        height: config.height,
        features: config.features,
        className: config.className,
      },
    })),
    
    closeTemporalResourcePopup: () => set((state) => ({
      temporalResourceMonitorState: 'hidden', // Changed from 'inline' to 'hidden' to default to off
      temporalResourcePopup: {
        ...state.temporalResourcePopup,
        isOpen: false,
      },
    })),
    
    // Method to force reset both monitors to hidden state
    resetToHidden: () => set({
      resourceMonitorState: 'hidden',
      temporalResourceMonitorState: 'hidden'
    }),
  }))
);
