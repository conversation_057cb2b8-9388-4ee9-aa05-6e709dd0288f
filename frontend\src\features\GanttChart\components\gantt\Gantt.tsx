// src/features/GanttChart/components/gantt/Gantt.tsx

import React, {
  useState,
  SyntheticEvent,
  useRef,
  useEffect,
  useLayoutEffect,
  useMemo,
  useCallback,
} from "react";
import { useWindowSize } from "@uidotdev/usehooks";
import { useShallow } from "zustand/react/shallow";
import { useParams } from "react-router-dom";

import { useTaskListStore } from "@/stores/taskLists";
import { useFilterOptionStore } from "@/stores/filterOption";
import {
  useNonDeletedDependencies,
  useNonDeletedTask,
} from "@/stores/hooks/ganttScheduler";
import { useGanttSchedulerStore } from "@/stores/ganttScheduler";
import { useStylingOptionStore } from "@/stores/stylingOption";

import { logger } from "@/common/helpers/logger";

import { Task } from "@/common/types";
import { seedDates, newGanttDateRange } from "@/common/helpers/date-helper";
import {
  filterTaskByFilterOption,
  removeHiddenTasks,
  sortDisplayTasks,
} from "@/common/helpers/other-helper";
import { updateAllDepth } from "@/common/helpers/depth-helper";
import { useDisableSelection } from "@/common/hooks/useDisableSelection";

import { GanttProps } from "@/features/GanttChart/types";
import { GridProps } from "../grid/Grid";
import { CalendarProps } from "../calendar/Calendar";
import { BarProps } from "@/features/GanttChart/types";
import TaskList, { TaskListProps } from "../task-list/TaskList";
import { BarSpan } from "../../types/barSpan";
import { convertToBarSpans } from "../../helpers/barHelper";
import { GanttEvent } from "../../types/ganttTaskActions";
import { DateSetup } from "../../types/dateSetup";
import { HorizontalScroll } from "../other/HorizontalScroll";
import HorizontalResizer, {
  HorizontalResizerProps,
} from "../other/HorizontalResizer";
import { useScroll } from "../../hooks/useScroll";
import { useResourceListColumns } from "../../hooks/useResourceListColumns";
import { useTemporalResourceListColumns } from "../../hooks/useTemporalResourceListColumns";
import { useResize } from "../../hooks/useResize";
import { useChartModeStore } from "@/stores/chartModeStore";
import ListTaskGanttView from "./ListTaskGanttView";
import { Separator } from "@radix-ui/react-dropdown-menu";
import { useMonitorHeights } from "@/stores/monitorHeights";

export const Gantt: React.FunctionComponent<GanttProps> = ({
  tasks,
  commentaries,
  headerHeight = 50,
  columnWidth = 60,
  listCellWidth = 155,
  rowHeight = 50,
  gripAreaWidth = 0,
  preStepsCount = 1,
  locale = "ja-JP",
  barFill = 60,
  barCornerRadius = 3,
  rtl = false,
  handleWidth = 8,
  timeStep = 3_600_000, // 1 hour
  arrowColor = "grey",
  fontFamily = "Arial, Roboto, Oxygen, Ubuntu, Cantarell, Fira Sans, Droid Sans, Helvetica Neue",
  fontSize = "14px",
  arrowIndent = 20,
  todayColor = "rgba(252, 248, 227, 0.5)",
  viewDate,
  abstViewTask,
  readOnly,
  spanDetailViewClose,
  openSpanEditor,
  handleFixedChange,
  setTaskIsChanged,
  onDateChange,
  onStartEndChange,
  onProgressChange,
  onDoubleClick,
  onClick,
  onDelete,
  onExpanderClick,
  onMultiSelect,
  selectedTaskIds,
}) => {
  // ------------------- Global and Local States -------------------
  const dependencies = useNonDeletedDependencies();
  const viewMode = useStylingOptionStore.use.viewMode();
  const isViewModeChanging = useStylingOptionStore.use.isViewModeChanging();
  const scheduleDay = useGanttSchedulerStore.use.scheduleDay();
  const taskListFullWidth = useTaskListStore(
    useShallow((state) => {
      let totalWidth = 0;
      state.columns.forEach((col) => {
        totalWidth += col.width;
      });
      totalWidth += gripAreaWidth;
      return totalWidth;
    }),
  );

  const filterOption = useFilterOptionStore.use.filter();
  const sortState = useGanttSchedulerStore.use.sortState();

  const [dateSetup, setDateSetup] = useState<DateSetup>(() => {
    const [startDate, endDate] = newGanttDateRange(
      tasks,
      viewMode,
      preStepsCount,
      scheduleDay.start,
      scheduleDay.end,
    );
    return { viewMode, dates: seedDates(startDate, endDate, viewMode) };
  });
  const [currentViewDate, setCurrentViewDate] = useState<Date | undefined>(
    undefined,
  );

  const [svgContainerWidth, setSvgContainerWidth] = useState(0);
  const [svgContainerHeight, setSvgContainerHeight] = useState(0);
  const [barTasks, setBarSpans] = useState<BarSpan[]>([]);
  const [ganttEvent, setGanttEvent] = useState<GanttEvent>({ action: "" });
  const taskHeight = useMemo(
    () => (rowHeight * barFill) / 100,
    [rowHeight, barFill],
  );
  const [failedTask, setFailedTask] = useState<BarSpan | null>(null);

  const { resourceColumns, changeColumnWidth, totalColumnWidth } =
    useResourceListColumns([
      { name: "name", width: 100 },
      { name: "quantity", width: 100 },
    ]);
  const {
    temporalResourceColumns,
    changeColumnWidth: changeTemporalColumnWidth,
    totalColumnWidth: totalTemporalColumnWidth,
  } = useTemporalResourceListColumns([{ name: "name", width: 100 }]);

  // Max possible widths for the resource tables
  const listFullWidth = taskListFullWidth;

  // Resizable widths & heights
  const { size: listWidth, handleChangeSize: handleChangeListWidth } =
    useResize(310, 0, listFullWidth, "task-list-resource-table-width");

  /** Gantt chart total width in px */
  const svgWidth = dateSetup.dates.length * columnWidth;

  /** Gantt chart total *content* height in px */
  const ganttFullHeight = barTasks.length * rowHeight ;

  // Create properly typed refs to match ListTaskGanttView's expectations
  const wrapperRef = useRef<HTMLDivElement>(null) as React.RefObject<HTMLDivElement>;
  const taskGanttRef = useRef<HTMLDivElement>(null) as React.RefObject<HTMLDivElement>;
  const taskListRef = useRef<HTMLDivElement>(null) as React.RefObject<HTMLDivElement>;
  const resourceGridRef = useRef<HTMLDivElement>(null);
  const temporalResourceGridRef = useRef<HTMLDivElement>(null);

  const showResourceMonitor = useStylingOptionStore.use.showResourceMonitor();
  const showTemporalResourceMonitor =
    useStylingOptionStore.use.showTemporalResourceMonitor();
  const isAnyMonitorShown = showResourceMonitor || showTemporalResourceMonitor;
  const scrollYBuffer = rowHeight;

  // ------------------- Print Mode -------------------
  const printMode = useChartModeStore((state) => state.printMode);

  // ------------------- Scroll States -------------------
  // Horizontal scroll for Gantt
  const {
    scroll: scrollX,
    setScroll: setScrollX,
    handleWheel,
  } = useScroll({
    ref: taskGanttRef as React.RefObject<HTMLElement>,
    type: "horizontal",
    max: svgWidth,
  });

  // Vertical scroll for Gantt & TaskList
  const { scroll: scrollY, setScroll: setScrollY } = useScroll({
    ref: wrapperRef as React.RefObject<HTMLElement>,
    type: "vertical",
    max: ganttFullHeight - ganttFullHeight + scrollYBuffer,
  });

  // Horizontal scroll for TaskList
  const { scroll: taskListScrollX, setScroll: setTaskListScrollX } = useScroll({
    ref: taskListRef as React.RefObject<HTMLElement>,
    type: "horizontal",
    max: listFullWidth,
  });

  // We still need to disable text selection during space-dragging
  const [isDraggingLocal, setIsDraggingLocal] = useState(false);
  useDisableSelection(isDraggingLocal);

  // Attach wheel listener to Resource Monitor's grid
  useEffect(() => {
    const refCurrent = resourceGridRef.current;
    refCurrent?.addEventListener("wheel", handleWheel, { passive: false });

    return () => {
      refCurrent?.removeEventListener("wheel", handleWheel);
    };
  }, [resourceGridRef, handleWheel]);

  // Attach wheel listener to Temporal Resource Monitor's grid
  useEffect(() => {
    const refCurrent = temporalResourceGridRef.current;
    refCurrent?.addEventListener("wheel", handleWheel, { passive: false });

    return () => {
      refCurrent?.removeEventListener("wheel", handleWheel);
    };
  }, [temporalResourceGridRef, handleWheel]);

  // Compute the full BarSpan[] for all tasks (unfiltered)
  const [allBarSpans, setAllBarSpans] = useState<BarSpan[]>([]);

  // Create a stable cache key for barSpan calculations
  const barSpanCacheKey = useMemo(() => {
    return `${tasks.length}_${dependencies.length}_${columnWidth}_${rowHeight}_${taskHeight}_${barCornerRadius}_${handleWidth}_${rtl}_${dateSetup.dates.length}`;
  }, [tasks.length, dependencies.length, columnWidth, rowHeight, taskHeight, barCornerRadius, handleWidth, rtl, dateSetup.dates.length]);

  // Memoize the conversion of tasks to bar spans for better performance
  const memoizedFullBarSpans = useMemo(() => {
    // First calculate depths for all tasks
    const tasksWithDepths = updateAllDepth([...tasks]);

    return convertToBarSpans(
      tasksWithDepths,
      dependencies,
      dateSetup.dates,
      columnWidth,
      rowHeight,
      taskHeight,
      barCornerRadius,
      handleWidth,
      rtl,
    );
  }, [barSpanCacheKey, tasks, dependencies, dateSetup.dates]);

  // Memoize filtered and sorted tasks separately from bar span calculation
  const memoizedSortedTasks = useMemo(() => {
    let filteredTasks: Task[];
    if (onExpanderClick) {
      filteredTasks = removeHiddenTasks([...tasks]);
    } else {
      filteredTasks = [...tasks];
    }

    filteredTasks = filterTaskByFilterOption(filteredTasks, filterOption);
    return sortDisplayTasks(filteredTasks, sortState);
  }, [tasks, filterOption, sortState, onExpanderClick]);

  // Optimize date setup calculation - use all tasks for date range calculation
  const memoizedDateSetup = useMemo(() => {
    const [startDate, endDate] = newGanttDateRange(
      tasks, // Use all tasks for date range calculation
      viewMode,
      preStepsCount,
      scheduleDay.start,
      scheduleDay.end,
    );
    const newDates = seedDates(startDate, endDate, viewMode);

    return { dates: newDates, viewMode };
  }, [
    tasks.length,
    viewMode,
    preStepsCount,
    scheduleDay.start?.getTime(),
    scheduleDay.end?.getTime(),
  ]);

  // Filter existing bar spans based on visibility rules (hideChildren)
  const memoizedFilteredBarSpans = useMemo(() => {
    // FIRST: Calculate depths for all tasks
    let processedTasks = updateAllDepth([...tasks]);

    // Apply user filters (search, date ranges, etc.)
    processedTasks = filterTaskByFilterOption(processedTasks, filterOption);

    // Then apply expand/collapse logic only if we have expander functionality
    if (onExpanderClick) {
      // Get projects that have hideChildren = true (collapsed)
      const collapsedProjects = processedTasks.filter(t =>
        t.type === 'project' && t.hideChildren === true
      );

      // If there are collapsed projects, filter out their children
      if (collapsedProjects.length > 0) {
        const hiddenTaskIds = new Set<string>();

        // For each collapsed project, find all its descendants
        collapsedProjects.forEach(project => {
          const findChildren = (parentId: string) => {
            const children = processedTasks.filter(t => t.project === parentId);
            children.forEach(child => {
              hiddenTaskIds.add(child.id);
              // Recursively find children of projects
              if (child.type === 'project') {
                findChildren(child.id);
              }
            });
          };
          findChildren(project.id);
        });

        processedTasks = processedTasks.filter(t => !hiddenTaskIds.has(t.id));
      }
    }

    // Apply sorting to maintain hierarchy
    processedTasks = sortDisplayTasks(processedTasks, sortState);

    // Create lookup for quick filtering of BarSpans
    const visibleTaskIds = new Set(processedTasks.map(t => t.id));

    // Filter BarSpans to match visible tasks
    const filteredBarSpans = memoizedFullBarSpans.filter(barSpan =>
      visibleTaskIds.has(barSpan.id)
    );

    return filteredBarSpans;
  }, [memoizedFullBarSpans, tasks, filterOption, sortState, onExpanderClick]);

  // Simplified useLayoutEffect that uses all memoized values
  useLayoutEffect(() => {
    // Only update if dateSetup actually changed
    if (dateSetup.dates !== memoizedDateSetup.dates || dateSetup.viewMode !== memoizedDateSetup.viewMode) {
      setDateSetup(memoizedDateSetup);
    }

    // Set all the memoized values
    setAllBarSpans(memoizedFullBarSpans);
    setBarSpans(memoizedFilteredBarSpans);
  }, [
    memoizedFullBarSpans,
    memoizedDateSetup.dates,
    memoizedDateSetup.viewMode,
    memoizedFilteredBarSpans
  ]);

  // Center on "viewDate" if specified
  useEffect(() => {
    if (
      viewMode === dateSetup.viewMode &&
      ((viewDate && !currentViewDate) ||
        (viewDate && currentViewDate?.valueOf() !== viewDate.valueOf()))
    ) {
      const dates = dateSetup.dates;
      const index = dates.findIndex(
        (d, i) =>
          viewDate.valueOf() >= d.valueOf() &&
          i + 1 !== dates.length &&
          viewDate.valueOf() < dates[i + 1].valueOf(),
      );
      if (index === -1) {
        return;
      }
      setCurrentViewDate(viewDate);
      setScrollX(columnWidth * index);
    }
  }, [
    viewDate,
    columnWidth,
    dateSetup.dates,
    dateSetup.viewMode,
    viewMode,
    currentViewDate,
    setCurrentViewDate,
  ]);

  // Listen for Gantt events (move, delete, etc.)
  useEffect(() => {
    const { changedTask, action } = ganttEvent;
    if (changedTask && !readOnly) {
      logger.log(`Gantt Event: ${action} ${changedTask.id} ${changedTask.name}`);
      if (action === "delete") {
        setGanttEvent({ action: "" });
        setBarSpans(barTasks.filter((t) => t.id !== changedTask.id));
      } else if (
        action === "move" ||
        action === "end" ||
        action === "start" ||
        action === "progress"
      ) {
        const prevStateTask = barTasks.find((t) => t.id === changedTask.id);
        if (prevStateTask) {
          if (
            !prevStateTask.start ||
            !prevStateTask.end ||
            (changedTask.start &&
              changedTask.end &&
              (prevStateTask.start.getTime() !== changedTask.start.getTime() ||
                prevStateTask.end.getTime() !== changedTask.end.getTime() ||
                prevStateTask.progress !== changedTask.progress))
          ) {
            if (setTaskIsChanged) setTaskIsChanged(true);
            if (spanDetailViewClose) spanDetailViewClose();
            const newTaskList = barTasks.map((t) =>
              t.id === changedTask.id ? changedTask : t,
            );
            setBarSpans(newTaskList);
          }
        }
      }
    }
  }, [ganttEvent, barTasks, readOnly, setTaskIsChanged, spanDetailViewClose]);

  // Handle a failedTask
  useEffect(() => {
    if (failedTask) {
      setBarSpans(
        barTasks.map((t) => (t.id !== failedTask.id ? t : failedTask)),
      );
      setFailedTask(null);
    }
  }, [failedTask, barTasks]);

  // Adjust container widths & heights
  useEffect(() => {
    if (wrapperRef.current) {
      setSvgContainerWidth(wrapperRef.current.offsetWidth - listWidth);
    }
  }, [wrapperRef, listWidth]);

  // Add viewMode change tracking
  const prevViewModeRef = useRef(viewMode);

  useEffect(() => {
    // If viewMode has changed, add transition class
    if (prevViewModeRef.current !== viewMode) {
      prevViewModeRef.current = viewMode;
    }
  }, [viewMode]);

  // Add animation wrapper class to container
  const ganttContainerClass = isViewModeChanging ? 'viewmode-transition' : '';

  const handleDateChange = useCallback((taskId: string, newStart: Date, newEnd: Date) => {
    if (onDateChange) {
      // Find the task by ID and pass it to the original function
      const task = barTasks.find(t => t.id === taskId);
      if (task) {
        const updatedTask = { ...task, start: newStart, end: newEnd };
        onDateChange(updatedTask, [], {});
      }
    }
  }, [onDateChange, barTasks]);

  // Get the current chart mode to check if we're in approval mode
  const { scheduleId } = useParams<{ scheduleId: string }>();
  const chartMode = useChartModeStore((s) => s.getScheduleChartMode(scheduleId || ""));
  const isApprovalMode = chartMode === "approval";

  // wrapper on multi-select to convert task IDs to tasks
  const handleMultiSelect = useCallback((taskIds: string[], isSelected: boolean) => {
    // Return early if in approval mode
    if (isApprovalMode || !onMultiSelect) {
      return;
    }

    // Convert task IDs to task objects
    const tasks = taskIds.map(id => {
      const task = barTasks.find(t => t.id === id);
      return task as Task;
    }).filter(Boolean);

    onMultiSelect(tasks);
  }, [onMultiSelect, barTasks, isApprovalMode]);

  // Local expander click
  const handleLocalExpanderClick = useCallback(
    (task: Task) => {
      if (onExpanderClick) {
        // If hideChildren is undefined, default to false (expanded)
        const currentHideChildren = task.hideChildren ?? false;
        onExpanderClick({ ...task, hideChildren: !currentHideChildren });
      }
    },
    [onExpanderClick],
  );

  // Double click on task name -> re-center Gantt
  const handleDoubleClickTaskName = useCallback(
    (taskId: string) => {
      const focusBarSpan = barTasks.find((t) => t.id === taskId);
      if (focusBarSpan && focusBarSpan.x1 !== 0) {
        setScrollX(focusBarSpan.x1 - columnWidth);
      }
    },
    [barTasks, columnWidth, setScrollX],
  );

  // Horizontal scroll sync: Gantt
  const handleScrollX = useCallback(
    (event: SyntheticEvent<HTMLDivElement>) => {
      const scrollLeft = event.currentTarget.scrollLeft;
      setScrollX((prev) => (prev !== scrollLeft ? scrollLeft : prev));
    },
    [setScrollX],
  );

  // Horizontal scroll sync: TaskList
  const handleTaskListScrollX = useCallback(
    (event: SyntheticEvent<HTMLDivElement>) => {
      const scrollLeft = event.currentTarget.scrollLeft;
      setTaskListScrollX((prev) => (prev !== scrollLeft ? scrollLeft : prev));
    },
    [setTaskListScrollX],
  );

  const gridProps: GridProps = {
    columnWidth,
    svgWidth,
    rowHeight,
    dates: dateSetup.dates,
    todayColor,
    rtl,
  };

  const tableProps: TaskListProps = {
    tasks: barTasks,
    allTasks: memoizedFullBarSpans, // Pass complete task list for expander logic
    headerHeight,
    taskListWidth: listWidth,
    fontFamily,
    fontSize,
    rowHeight,
    gripAreaWidth,
    scrollX: taskListScrollX,
    scrollY,
    locale,
    onExpanderClick: handleLocalExpanderClick,
    handleFixedChange: handleFixedChange ?? ((task: Task) => {}),
    handleDoubleClickTaskName,
    onMultiSelect,
    selectedTaskIds,
    selectedTask: undefined,
  };

  // Define barProps with the correct type
  const barProps: BarProps = {
    barTasks: barTasks,
    dates: dateSetup.dates,
    ganttEvent,
    rowHeight,
    taskHeight,
    columnWidth,
    arrowColor,
    timeStep,
    fontFamily,
    fontSize,
    arrowIndent,
    svgWidth,
    rtl,
    setGanttEvent,
    setFailedTask,
    onDateChange: onDateChange ? ((task: Task, children: Task[]) => { onDateChange(task, children, {}); }) : undefined,
    onStartEndChange: onStartEndChange ? ((task: Task) => { onStartEndChange(task); }) : undefined,
    onProgressChange: onProgressChange ? ((task: Task, children: Task[]) => { onProgressChange(task, children, {}); }) : undefined,
    onDoubleClick,
    onClick,
    onDelete: onDelete ? ((task?: Task | undefined) => { if (task) onDelete(task); }) : (task?: Task | undefined) => {},
    selectedTask: undefined,
  };
  const calendarProps: CalendarProps = {
    dateSetup,
    locale,
    headerHeight,
    columnWidth,
    fontFamily,
    fontSize,
    rtl,
  };

  const horizontalResizerProps: HorizontalResizerProps = {
    size: listWidth,
    handleChangeSize: handleChangeListWidth,
  };

  // Debug: log tasks and filter state (commented out for performance)
  // console.log('[Gantt DEBUG] tasks:', tasks);
  // console.log('[Gantt DEBUG] filterOption:', filterOption);

  // Example: Memoize onExpanderClick and handleFixedChange if not already
  const memoizedOnExpanderClick = useCallback((task: Task) => {
    if (onExpanderClick) onExpanderClick(task);
  }, [onExpanderClick]);
  const memoizedHandleFixedChange = useCallback((task: Task) => {
    if (handleFixedChange) handleFixedChange(task);
  }, [handleFixedChange]);
  // Add useCallback to other handlers as needed (e.g., onMultiSelect, onDoubleClick, etc.)

  return (
    <div ref={wrapperRef} style={{ height: '100%', border: '2px solid green' }}>
        <ListTaskGanttView
          wrapperRef={wrapperRef}
          taskListRef={taskListRef}
          taskGanttRef={taskGanttRef}
          listCellWidth={listCellWidth}
          tableProps={tableProps}
          horizontalResizerProps={horizontalResizerProps}
          gridProps={gridProps}
          calendarProps={calendarProps}
          barProps={barProps}
          scrollX={scrollX}
          scrollY={scrollY}
        readOnly={readOnly}
          selectedTaskIds={selectedTaskIds}
          ganttEvent={ganttEvent}
          arrowIndent={arrowIndent}
          rowHeight={rowHeight}
          svgContainerHeight={svgContainerHeight}
          svgContainerWidth={svgContainerWidth}
          locale={locale}
          fontSize={fontSize}
          headerHeight={headerHeight}
          listWidth={listWidth}
          rtl={rtl}
          svgWidth={svgWidth}
          columnWidth={columnWidth}
        abstViewTask={abstViewTask ?? null}
        spanDetailViewClose={spanDetailViewClose ?? (() => {})}
        openSpanEditor={openSpanEditor ? ((task?: Task | undefined) => { if (task) openSpanEditor(task); }) : ((task?: Task | undefined) => {})}
        onDelete={onDelete ? ((task?: Task | undefined) => { if (task) onDelete(task); }) : (task?: Task | undefined) => {}}
          onDateChange={handleDateChange}
          onMultiSelect={handleMultiSelect}
          setScrollX={setScrollX}
          setScrollY={setScrollY}
          setIsDragging={setIsDraggingLocal}
        commentaries={commentaries ?? []}
          allBarSpans={allBarSpans}
          dependencies={dependencies}
          barCornerRadius={barCornerRadius}
          handleWidth={handleWidth}
        />
        <div className="flex sticky bottom-0 z-10">
          {listCellWidth ? (
            <HorizontalScroll
              svgWidth={taskListFullWidth}
              scroll={taskListScrollX}
              minWidth={listWidth}
              onScroll={handleTaskListScrollX}
            />
          ) : null}
          <HorizontalScroll
            svgWidth={svgWidth}
            scroll={scrollX}
            onScroll={handleScrollX}
          />
        </div>
    </div>
  );
};

export default Gantt;
