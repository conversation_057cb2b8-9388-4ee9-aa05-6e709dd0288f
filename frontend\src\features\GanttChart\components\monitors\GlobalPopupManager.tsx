import React from 'react';
import { useMonitorStore } from '@/stores/monitorStore';
import PopOutWindow from '@/components/ui/pop-out-window';
import { PopupResourceMonitor } from './PopupResourceMonitor';
import { PopupTemporalResourceMonitor } from './PopupTemporalResourceMonitor';
import { useMonitorProps } from '@/features/GanttChart/hooks/useMonitorProps';

// This component manages popup windows at a high level to avoid unmounting issues
export const GlobalPopupManager: React.FC = () => {
  const resourcePopup = useMonitorStore(state => state.resourcePopup);
  const temporalResourcePopup = useMonitorStore(state => state.temporalResourcePopup);
  const closeResourcePopup = useMonitorStore(state => state.closeResourcePopup);
  const closeTemporalResourcePopup = useMonitorStore(state => state.closeTemporalResourcePopup);

  // Get all the monitor props from our centralized hook
  let resourceMonitorProps = null;
  let temporalResourceMonitorProps = null;
  
  try {
    const props = useMonitorProps();
    resourceMonitorProps = props.resourceMonitorProps;
    temporalResourceMonitorProps = props.temporalResourceMonitorProps;
    console.log('[GlobalPopupManager] Monitor props loaded successfully:', { resourceMonitorProps, temporalResourceMonitorProps });
  } catch (error) {
    console.error('[GlobalPopupManager] Error loading monitor props:', error);
  }

  return (
    <>
      {/* Resource Monitor Popup */}
      {resourcePopup.isOpen && (
        <PopOutWindow
          title={resourcePopup.title}
          width={1200}
          height={800}
          features={resourcePopup.features}
          className={resourcePopup.className}
          onClose={closeResourcePopup}
        >
          {resourceMonitorProps ? (
            <PopupResourceMonitor {...resourceMonitorProps} />
          ) : (
            <div style={{ padding: '20px', fontSize: '16px' }}>
              <h2>Resource Monitor - Debug View</h2>
              <p>Props failed to load. Check console for errors.</p>
              <p>Popup is open: {resourcePopup.isOpen ? 'Yes' : 'No'}</p>
            </div>
          )}
        </PopOutWindow>
      )}

      {/* Temporal Resource Monitor Popup */}
      {temporalResourcePopup.isOpen && (
        <PopOutWindow
          title={temporalResourcePopup.title}
          width={1200}
          height={800}
          features={temporalResourcePopup.features}
          className={temporalResourcePopup.className}
          onClose={closeTemporalResourcePopup}
        >
          {temporalResourceMonitorProps ? (
            <PopupTemporalResourceMonitor {...temporalResourceMonitorProps} />
          ) : (
            <div style={{ padding: '20px', fontSize: '16px' }}>
              <h2>Temporal Resource Monitor - Debug View</h2>
              <p>Props failed to load. Check console for errors.</p>
              <p>Popup is open: {temporalResourcePopup.isOpen ? 'Yes' : 'No'}</p>
            </div>
          )}
        </PopOutWindow>
      )}
    </>
  );
};

export default GlobalPopupManager; 