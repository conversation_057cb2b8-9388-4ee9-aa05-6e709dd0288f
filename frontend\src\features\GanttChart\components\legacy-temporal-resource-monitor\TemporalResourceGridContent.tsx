import { useMemo } from "react";
import { useShallow } from "zustand/react/shallow";

import { useNonDeletedTask } from "@/stores/hooks/ganttScheduler";
import { useGanttSchedulerStore } from "@/stores/ganttScheduler";

import { DateHelperScales, addToDate } from "@/common/helpers/date-helper";
import { TemporalResource, Task, DurationData } from "@/common/types";

import { taskXCoordinate, taskYCoordinate } from "../../helpers/barHelper";
import CutDownBar from "./CutDownBar";

/**
 * {@link TemporalResourceGridContent}のprops
 */
export interface TemporalResourceGridContentProps {
  /** カレンダーの日付リスト */
  dates: Date[];
  /** 表示する期間付きリソースのリスト */
  resources: TemporalResource[];
  /** 表の横幅 */
  columnWidth: number;
  /** 表の縦幅 */
  rowHeight: number;
  /** 表のパディングサイズ */
  paddingSize?: number;
}

/**
 * 期間付きリソースの使用状況のグラフ
 *
 * @category Component
 */
const TemporalResourceGridContent: React.FC<
  TemporalResourceGridContentProps
> = ({ dates, resources, columnWidth, rowHeight, paddingSize = 7 }) => {
  const tasks = useNonDeletedTask();
  const scale: DateHelperScales = useGanttSchedulerStore(
    useShallow((state) => {
      const timeUnit = state.timeUnit;
      if (timeUnit === "hours") return "hour";
      else return "day";
    }),
  );

  const resourceGraph: React.ReactNode[] = useMemo(() => {
    return resources.map((r, index) => {
      const resourceGraph: React.ReactNode[] = [];
      const quantityTransitions = calcQuantityTransitions(
        tasks,
        r.id,
        r.durationData,
        scale,
      );
      const y = taskYCoordinate(index, rowHeight, rowHeight - paddingSize * 2);
      quantityTransitions.forEach(({ date, end, quantity, total }, index) => {
        /** 表示の幅は、変化があった箇所から次に変化があった箇所または、durationDataのendまでとする */
        const x1 = taskXCoordinate(date, dates, columnWidth);
        const nextDate =
          quantityTransitions.length > index + 1
            ? quantityTransitions[index + 1].date
            : end;
        const x2 = taskXCoordinate(nextDate, dates, columnWidth);
        resourceGraph.push(
          <svg
            key={`${r.id} ${date.toISOString()}`}
            xmlns="http://www.w3.org/2000/svg"
            width={x2 - x1}
            height={rowHeight - paddingSize * 2}
            x={x1}
            y={y}
          >
            <CutDownBar total={total} current={quantity} />
          </svg>,
        );
      });
      return resourceGraph;
    });
  }, [columnWidth, dates, paddingSize, resources, rowHeight, scale, tasks]);

  return <g>{resourceGraph}</g>;
};

/**
 * 期間付きリソースの期間中の変化をリストにして返す関数。
 * 変化のあったdateとその時の残量をまとめたオブジェクトをリストにして返す。
 *
 * @param {Task[]} tasks - 全てのタスク
 * @param {string} temporalResourceId - 集計する期間付きリソースのID
 * @param {DurationData[]} durationData - 集計する期間付きリソースのdurationData
 * @param {DateHelperScales} scale - 時間解像度
 * @returns {{date: Date; end: Date; quantity: number; total: number;}[]} 集計結果
 */
const calcQuantityTransitions = (
  tasks: Task[],
  temporalResourceId: string,
  durationData: DurationData[],
  scale: DateHelperScales,
): {
  date: Date;
  end: Date;
  quantity: number;
  total: number;
}[] => {
  const quantityTransition: {
    date: Date;
    end: Date;
    quantity: number;
    total: number;
  }[] = [];
  /** durationDataごとに、1単位時間進めて、期間付きリソースの量に変化があったのかを記録していく */
  durationData.forEach(({ start, end, quantity }) => {
    let targetDate = start;
    let currentQuantity = quantity;
    /** 最初は、消費量が無くても登録するため、while文の前に実行 */
    const nextDate = addToDate(targetDate, 1, scale);
    const totalConsumption = getTotalTemporalResourceConsumption(
      tasks,
      targetDate,
      nextDate,
      temporalResourceId,
    );
    currentQuantity -= totalConsumption;
    quantityTransition.push({
      date: targetDate,
      end: end,
      quantity: currentQuantity,
      total: quantity,
    });
    targetDate = nextDate;
    /** 時間単位ごとに消費量を計算し、消費されている場合は、リストに記録 */
    while (targetDate.getTime() < end.getTime()) {
      const nextDate = addToDate(targetDate, 1, scale);
      const totalConsumption = getTotalTemporalResourceConsumption(
        tasks,
        targetDate,
        nextDate,
        temporalResourceId,
      );
      if (totalConsumption !== 0) {
        currentQuantity -= totalConsumption;
        quantityTransition.push({
          date: targetDate,
          end: end,
          quantity: currentQuantity,
          total: quantity,
        });
      }
      targetDate = nextDate;
    }
  });
  return quantityTransition;
};

/**
 * 指定した期間付きリソースが期間中[start ~ end)に消費した
 * 期間付きリソースの数の合計を求める関数
 *
 * @param {Task[]} tasks - 全てのタスク
 * @param {Date} start - 開始
 * @param {Date} end - 終了
 * @param {string} temporalResourceId - 集計する期間付きリソースのID
 * @returns {number} - 期間中に消費した期間付きリソースの数の合計
 */
const getTotalTemporalResourceConsumption = (
  tasks: Task[],
  start: Date,
  end: Date,
  temporalResourceId: string,
): number => {
  // startの位置がstart ~ endの外側にあるtaskは除いておく。
  const targetTasks = tasks.filter(
    (t) =>
      t.temporalResource.some(
        (r) => r.temporalResourceId === temporalResourceId,
      ) &&
      t.start !== null &&
      t.end !== null &&
      t.start.getTime() >= start.getTime() &&
      t.start.getTime() < end.getTime(),
  );
  if (targetTasks.length === 0) return 0;
  return targetTasks.reduce((prev, current) => {
    const targetResource = current.temporalResource.find(
      (r) => r.temporalResourceId === temporalResourceId,
    );
    const consumption = targetResource?.consumption ?? 0;
    return prev + consumption;
  }, 0);
};

export default TemporalResourceGridContent;
