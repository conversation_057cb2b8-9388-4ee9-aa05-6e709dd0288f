// src/features/GanttChart/components/task-list/TaskListTableRow.tsx
import { CSS } from "@dnd-kit/utilities";
import { useSortable } from "@dnd-kit/sortable";
import { useShallow } from "zustand/react/shallow";

import { Column } from "@/stores/taskLists";
import { useGanttSchedulerStore } from "@/stores/ganttScheduler";

import { Task } from "@/common/types";
import { cn } from "@/common/helpers/tailwindMerge";

import { BarSpan } from "../../types/barSpan";
import TaskListCells from "./TaskListCells";
import { css } from "@emotion/react";

export interface TaskListTableRowProps {
  index: number;
  task: BarSpan;
  depth: number;
  displayColumns: Column[];
  rowHeight: number;
  gripAreaWidth: number;
  disabled?: boolean;
  toLocaleDateString: (
    date: Date,
    dateTimeOptions: Intl.DateTimeFormatOptions
  ) => string;
  onExpanderClick: (task: Task) => void;
  handleFixedChange: (task: Task) => void;
  handleDoubleClickTaskName: (taskId: string) => void;

  /** For multi‐select usage: */
  selectedTaskIds?: string[];
  onMultiSelect?: (tasks: Task[]) => void;
  allTasks: BarSpan[];
}

const TaskListTableRow: React.FC<TaskListTableRowProps> = ({
  index,
  task,
  depth,
  displayColumns,
  rowHeight,
  gripAreaWidth,
  disabled,
  toLocaleDateString,
  onExpanderClick,
  handleFixedChange,
  handleDoubleClickTaskName,
  selectedTaskIds = [],
  onMultiSelect,
  allTasks,
}) => {
  // enable DnD only if neutral sort & not disabled
  const isDraggable = useGanttSchedulerStore(
    useShallow((state) => state.sortState.type === "neutral" && !disabled),
  );

  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    setActivatorNodeRef,
    isDragging,
  } = useSortable({ id: task.id, disabled: !isDraggable });
  const hightlightColor = "blue-400";

  return (
    <div
      style={{
        position: "relative",
        width: "100%",
        // Add thick bottom border to the last row
        ...(index === (allTasks?.length ?? 0) - 1 ? { borderBottom: '3px solid #888' } : {}),
      }}
    >
      <div
        className={cn(
          "table-row overflow-hidden text-ellipsis",
          (index + 1) % 2 === 0 ? "bg-gray-100" : "bg-white",
            // highlight if selected
            selectedTaskIds.includes(task.id) && `bg-${hightlightColor} opacity-75`,
          )}
        css={css`
          height: ${rowHeight}px;
        `}
        >
        {isDragging ? null : (
          <TaskListCells
            task={task}
            depth={depth}
            displayColumns={displayColumns}
            rowHeight={rowHeight}
            gripAreaWidth={gripAreaWidth}
            disabled={disabled}
            toLocaleDateString={toLocaleDateString}
            onExpanderClick={onExpanderClick}
            handleFixedChange={handleFixedChange}
            handleDoubleClickTaskName={handleDoubleClickTaskName}
            dragHandleProps={{
              setActivatorNodeRef,
              attributes,
              listeners,
            }}
            /** pass them to the cells: */
            selectedTaskIds={selectedTaskIds}
            onMultiSelect={onMultiSelect}
            allTasks={allTasks}
            isDragging={isDragging}
          />
        )}
      </div>
    </div>
  );
};

export default TaskListTableRow;
