/**
 * {@link RateBar}のprops
 */
export interface RateBarProps {
  /** 分子 */
  numerator: number;
  /** 分母 */
  denominator: number;
}

/**
 * 指定した割合の棒グラフを表示するコンポーネント。
 * 中心(y=50%)と100%としている。
 */
const RateBar: React.FC<RateBarProps> = ({ numerator, denominator }) => {
  // percetageを計算。分母が0になるものは、0%とする。
  const percentage =
    denominator !== 0 ? Math.round((numerator / denominator) * 100) : 0;

  // percentageによって色を決める
  let color = "black";
  if (percentage <= 40) color = "#4054CA";
  else if (percentage <= 60) color = "#44D29E";
  else if (percentage <= 80) color = "#D9E03F";
  else if (percentage <= 100) color = "#ED7922";
  else color = "#CA3259";
  return (
    <g>
      <rect
        className="duration-500 ease-in-out"
        x="0"
        y={`${100 - percentage / 2}%`}
        width="100%"
        height={`${percentage / 2}%`}
        fill={color}
      />
      <line className="stroke-red-600" x1="0" y1="50%" x2="100%" y2="50%" />
      {percentage !== 0 && (
        <text
          className="font-bold"
          x="50%"
          y="50%"
          textAnchor="middle"
          dominantBaseline="middle"
        >{`${percentage}%`}</text>
      )}
    </g>
  );
};

export default RateBar;
