import { Task } from "@/common/types";

/**
 * 与えられたプロジェクトのDepth（親プロジェクトの数）を返す関数
 *
 * @param project - 1つのプロジェクト情報
 * @param tasks - タスクのリスト
 * @return Depth (親プロジェクトの数)
 */
const calculateDepth = (project: Task, tasks: Task[]): number => {
  if (project.type !== "project") {
    throw Error("function calculateDepth takes an only project as a argument");
  }

  if (project.project) {
    const parent = tasks.find((t) => t.id === project.project);
    if (parent) {
      return calculateDepth(parent, tasks) + 1;
    } else {
      return 0;
    }
  } else {
    return 0;
  }
};

/**
 *  与えられたタスクのリストのDepthを更新する（project限定）関数
 *
 * @param tasks - タスクのリスト
 * @return Depthを更新した後のタスクのリスト
 */
export const updateAllDepth = (tasks: Task[]): Task[] => {
  const newTasks = [...tasks];

  // プロジェクトのDepth更新
  const projects = tasks.filter((t) => t.type === "project");
  const newProjects: Task[] = projects.map((project) => {
    return { ...project, depth: calculateDepth(project, tasks) };
  });

  // タスクの更新
  newProjects.forEach((project) => {
    const index = newTasks.findIndex((t) => t.id === project.id);
    if (index > -1) {
      newTasks[index] = project;
    }
  });

  return newTasks;
};

/**
 *  与えられたプロジェクトとその子プロジェクトのDepthを更新する関数
 *
 * @param project - 1つのプロジェクト情報
 * @param tasks - タスクのリスト
 * @return Depthを更新した後のタスクのリスト
 */
export const updateDepthWithChildren = (
  project: Task,
  tasks: Task[],
): Task[] => {
  const newTasks = [...tasks];

  // 全ての子プロジェクトのDepthを更新
  const newProjects: Task[] = [];
  let childProjects = [{ ...project }];
  let depth: number = calculateDepth(project, tasks);
  while (childProjects.length > 0) {
    for (let i = 0; i < childProjects.length; i++) {
      newProjects.push({ ...childProjects[i], depth });
    }

    // childProjetcs更新
    const newChildProjects: Task[] = [];
    childProjects.forEach((project) => {
      const children = newTasks.filter(
        (t) => t.project === project.id && t.type === "project",
      );
      newChildProjects.push(...children);
    });
    childProjects = newChildProjects;

    // Depth更新
    depth++;
  }

  // タスクの更新
  newProjects.forEach((project) => {
    const index = newTasks.findIndex((t) => t.id === project.id);
    if (index > -1) {
      newTasks[index] = project;
    }
  });

  return newTasks;
};

/**
 * 与えられたタスクのDepth（親プロジェクトの数）を返す関数
 *
 * @param task - 1つのタスク情報
 * @param tasks - タスクのリスト
 * @return Depth (親プロジェクトの数)
 */
export const getCurrentDepth = (task: Task, tasks: Task[]): number => {
  switch (task.type) {
    case "project":
      return task.depth ?? 0;
    case "task":
    case "milestone":
      if (task.project) {
        const project = tasks.find((t) => t.id === task.project);
        return typeof project?.depth !== "undefined" ? project.depth + 1 : 0;
      } else {
        return 0;
      }
    default:
      return 0;
  }
};
