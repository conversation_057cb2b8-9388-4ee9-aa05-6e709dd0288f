import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { ActionMeta, MultiValue } from "react-select";
import { Cog8ToothIcon } from "@heroicons/react/24/outline";

import { useResourceMonitorStore } from "@/stores/resourceMonitor";
import { useNonDeletedTemporalResource } from "@/stores/hooks/ganttScheduler";

import { Toggle } from "@/common/components/Inputs/Toggle";
import { Label } from "@/common/components/Other";
import { BaseSelect } from "@/common/components/Selects";
import { logger } from "@/common/helpers/logger";
import { isUndefined } from "@/common/helpers/typeGuard";

type onChangeMultiSelect = (
  newValue: MultiValue<{
    value: string;
    label: string;
  }>,
  actionMeta: ActionMeta<{
    value: string;
    label: string;
  }>,
) => void;

/**
 * 期間付きリソースモニターの設定画面
 */
const TemporalResourceMonitorSetting: React.FC = () => {
  // global state
  const { displayIds, isDisplayed } =
    useResourceMonitorStore.use.temporalResource();
  const temporalResource = useNonDeletedTemporalResource();
  const resourceOptions = temporalResource.map(({ id, name }) => ({
    value: id,
    label: name,
  }));
  const changeTemporalResourceIsDisplayed =
    useResourceMonitorStore.use.changeTemporalResourceIsDisplayed();
  const replaceTemporalResourceDisplayIds =
    useResourceMonitorStore.use.replaceTemporalResourceDisplayIds();

  const handleChangeDisplayIds: onChangeMultiSelect = (_, actionMeta) => {
    const { action, option, removedValue } = actionMeta;
    switch (action) {
      case "select-option":
        if (!isUndefined(option))
          replaceTemporalResourceDisplayIds([...displayIds, option.value]);
        break;
      case "remove-value":
        if (!isUndefined(removedValue))
          replaceTemporalResourceDisplayIds(
            displayIds.filter((id) => id !== removedValue.value),
          );
        break;
      case "clear":
        replaceTemporalResourceDisplayIds([]);
        break;
      case "pop-value":
      case "create-option":
      case "deselect-option":
        logger.warn(`Not Implemented action: ${action}`);
        break;
      default:
        break;
    }
  };

  return (
    <div className="border-t">
      <Accordion type="single" collapsible>
        <AccordionItem value="temporal-resource-monitor">
          <AccordionTrigger>
            <span className="flex items-center gap-2">
              <Cog8ToothIcon className="h-6 w-6 stroke-2" />
              期間付きリソースモニター
            </span>
          </AccordionTrigger>
          <AccordionContent>
            <div className="max-w-(--breakpoint-lg) space-y-3">
              <div>
                <Label>表示</Label>
                <Toggle
                  checked={isDisplayed}
                  onChange={() => {
                    changeTemporalResourceIsDisplayed();
                  }}
                />
              </div>
              <div>
                <Label>表示するリソース</Label>
                <BaseSelect
                  isMulti
                  closeMenuOnSelect={false}
                  options={resourceOptions}
                  value={resourceOptions.filter(({ value }) =>
                    displayIds.includes(value),
                  )}
                  onChange={handleChangeDisplayIds}
                />
              </div>
            </div>
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </div>
  );
};

export default TemporalResourceMonitorSetting;
