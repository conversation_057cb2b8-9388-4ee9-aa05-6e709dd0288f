// src/features/GanttChart/components/task-list/TaskList.tsx

import React, { useEffect, useRef, memo } from "react";
import { BarSpan } from "../../types/barSpan";
import { Task } from "@/common/types";

import TaskListTable, { TaskListTableProps } from "./TaskListTable";
import TaskListHeader, { TaskListHeaderProps } from "./TaskListHeader";

import { useTaskListColumns } from "../../hooks/useTaskListColumns";
import { useVirtualizer } from "@tanstack/react-virtual";
import { useChartModeStore } from "@/stores/chartModeStore";

export type TaskListProps = {
  tasks: BarSpan[];
  allTasks?: BarSpan[]; // Complete task list for expander logic
  headerHeight: number;
  taskListWidth: number;
  fontFamily: string;
  fontSize: string;
  rowHeight: number;
  gripAreaWidth: number;
  scrollX: number;
  scrollY: number;
  locale: string;
  selectedTask: BarSpan | undefined;
  disabled?: boolean;
  onExpanderClick: (task: Task) => void;
  handleFixedChange: (task: Task) => void;
  handleDoubleClickTaskName: (taskId: string) => void;

  // For multi‐select usage:
  onMultiSelect?: (tasks: Task[]) => void;
  selectedTaskIds?: string[];

  // Optional external row virtualizer (for sync with TaskGantt)
  rowVirtualizer?: ReturnType<typeof useVirtualizer>;
};

const TaskListComponent: React.FC<TaskListProps> = (props) => {
  const {
    tasks,
    allTasks,
    onMultiSelect,
    selectedTaskIds = [],
    headerHeight,
    fontFamily,
    fontSize,
    rowHeight,
    gripAreaWidth,
    taskListWidth,
    scrollX,
    scrollY,
    selectedTask,
    onExpanderClick,
    handleFixedChange,
    handleDoubleClickTaskName,
    locale,
    disabled,
    rowVirtualizer: externalRowVirtualizer, // Get the external virtualizer if provided
  } = props;

  const {
    displayColumns,
    setDisplayColumns,
    changeColumnWidth,
    changeColumnWidthGlobal,
    replaceLocalAndGlobal,
  } = useTaskListColumns();

  const verticalContainerRef = useRef<HTMLDivElement>(null);
  const horizontalContainerRef = useRef<HTMLDivElement>(null);

  // Always create our internal virtualizer
  const internalRowVirtualizer = useVirtualizer({
    count: tasks.length,
    getScrollElement: () => verticalContainerRef.current,
    estimateSize: () => rowHeight,
    overscan: 10, // Increased to reduce flashing
    scrollPaddingEnd: rowHeight,
  });

  // Use external virtualizer if provided, otherwise use our internal one
  const rowVirtualizer = externalRowVirtualizer || internalRowVirtualizer;

  // Keep horizontal scroll in sync
  useEffect(() => {
    if (horizontalContainerRef.current && verticalContainerRef.current) {
      horizontalContainerRef.current.scrollLeft = scrollX;
      verticalContainerRef.current.scrollLeft = scrollX;
    }
  }, [scrollX]);

  const printMode = useChartModeStore((state) => state.printMode);

  // Re-measure on rowHeight changes (only if using internal virtualizer)
  useEffect(() => {
    if (!externalRowVirtualizer) {
      rowVirtualizer.measure();
    }
  }, [rowHeight, rowVirtualizer, externalRowVirtualizer]);

  // Re‐measure on print mode (only if using internal virtualizer)
  useEffect(() => {
    if (!externalRowVirtualizer) {
      rowVirtualizer.measure();
    }
  }, [printMode, rowVirtualizer, externalRowVirtualizer]);

  // Let the header scroll horizontally so it stays aligned // CHANGED
  const handleHeaderScroll = (e: React.UIEvent<HTMLDivElement>) => {
    if (!verticalContainerRef.current) return;
    verticalContainerRef.current.scrollLeft = e.currentTarget.scrollLeft;
  };

  // Let the body's scroll mirror the header // CHANGED
  const handleBodyScroll = (e: React.UIEvent<HTMLDivElement>) => {
    if (!horizontalContainerRef.current) return;
    horizontalContainerRef.current.scrollLeft = e.currentTarget.scrollLeft;
  };

  // Props for header & table
  const headerProps: TaskListHeaderProps = {
    displayColumns,
    setDisplayColumns,
    changeColumnWidth,
    changeColumnWidthGlobal,
    replaceLocalAndGlobal,
    headerHeight,
    gripAreaWidth,
  };

  const tableProps: TaskListTableProps = {
    tasks,
    allTasks: allTasks || tasks, // Use allTasks if provided, otherwise fall back to tasks
    displayColumns,
    rowHeight,
    taskListWidth,
    gripAreaWidth,
    fontFamily,
    fontSize,
    locale,
    selectedTaskId: selectedTask ? selectedTask.id : "",
    disabled,
    onExpanderClick,
    handleFixedChange,
    handleDoubleClickTaskName,
    rowVirtualizer,
    // pass multi‐select props
    selectedTaskIds,
    onMultiSelect,
  };

  // Insert or rename the "select" column at the last position
  useEffect(() => {
    const existingSelectIdx = displayColumns.findIndex(c => c.name === "multiSelected");
    // remove the old one if it exists
    const columns = displayColumns.filter(col => col.name !== "multiSelected");

    if (existingSelectIdx < 0) {
      columns.unshift({ name: "multiSelected", width: 30 });
      replaceLocalAndGlobal(columns);
    }
  }, [displayColumns, replaceLocalAndGlobal]);

  return (
    <div id="task-list" style={{ fontFamily, fontSize, position: 'relative', willChange: 'transform' }}>
      {/* Header now scrolls horizontally */}
      <div
        className="m-0 p-0 border-b border-gray-300 overflow-x-scroll overflow-y-hidden no-scrollbar"
        ref={horizontalContainerRef}
        dir="ltr"
        style={{
          width: taskListWidth,
          height: headerHeight, // CHANGED: fixed height
          position: 'sticky',
          top: 0,
          zIndex: 20,
          backgroundColor: 'white',
          willChange: 'transform', // Optimize for GPU acceleration
        }}
        onScroll={handleHeaderScroll} // CHANGED
      >
        <TaskListHeader {...headerProps} />
      </div>

      {/* Body can also scroll horizontally + vertically */}
      <div
        className="m-0 p-0 bg-white no-scrollbar overflow-x-scroll overflow-y-hidden"
        ref={verticalContainerRef}
        id="task-list-body"
        style={{
          width: taskListWidth,
          height: "auto",
          paddingBottom: rowHeight,
          borderBottom: '3px solid #888',
          willChange: 'transform', // Optimize for GPU acceleration
          backfaceVisibility: 'hidden', // Prevent flickering in some browsers
        }}
        onScroll={handleBodyScroll}
      >
        <TaskListTable {...tableProps} />
      </div>
    </div>
  );
};

// Apply memo to the component
const TaskList = memo(TaskListComponent);
TaskList.displayName = 'TaskList';

export default TaskList;
