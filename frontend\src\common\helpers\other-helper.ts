//src/common/helpers/other-helper.ts
import { nanoid } from "nanoid";
import {
  FilterOption,
  Resource,
  SortState,
  Span,
  Task,
  TemporalResource,
} from "@/common/types";
import { isString, isUndefined } from "./typeGuard";
import { filterDateProperties } from "@/stores/filterOption";

/**
 * hideChildren = trueとなっているプロジェクトの子タスクを除く関数。
 *
 * @param tasks - タスクのリスト
 * @returns フィルタリングされたタスクのリスト
 */
export function removeHiddenTasks(tasks: Task[]): Task[] {
  // Identify tasks with hideChildren and type "project"
  const hiddenProjects = tasks.filter((t) => t.hideChildren && t.type === "project");
  // Collect all children of hidden projects
  const hiddenTaskIdsGroup1 = hiddenProjects.flatMap((project) =>
    tasks.filter((t) => t.project === project.id).map((t) => t.id)
  );
  // // Identify tasks with hide<PERSON>hildren and has entity (linked schedule)
  // const hiddenLinkedSchedules = tasks.filter((t) => t.hideChildren && t.entity);
  // const hiddenTaskIdsGroup2 = hiddenLinkedSchedules.flatMap((schedule) =>
  //   tasks.filter((t) => t.parentSchedule === t.entity).map((t) => t.id)
  // );

  // Filter out tasks that are children of hidden projects
  return tasks.filter((t) => !hiddenTaskIdsGroup1.includes(t.id));
  // return tasks.filter((t) => !hiddenTaskIdsGroup1.includes(t.id) && !hiddenTaskIdsGroup2.includes(t.id));
}

/**
 * 空のタスク/プロジェクト/マイルストーンを生成する関数。
 *
 * @returns 空のタスク/プロジェクト/マイルストーン
 */
export const makeEmptySpan = (): Span => {
  return {
    id: nanoid(),
    type: "task",
    name: "",
    start: null,
    end: null,
    startNoEarlierThan: null,
    deadline: null,
    progress: 0,
    time: 5,
    value: 5,
    description: "",
    resource: [],
    temporalResource: [],
    fixed: false,
    crudType: "create",
  };
};

/**
 * タスクのリストをfilterOptionの設定に応じてフィルタリングする関数。
 *
 * @param tasks - タスクのリスト
 * @param filterOption - フィルタリングの設定
 * @returns フィルタリングされたタスクのリスト
 */
export const filterTaskByFilterOption = (
  tasks: Span[],
  filterOption: FilterOption,
): Span[] => {
  let filteredTasks: Span[] = [...tasks];
  filteredTasks = tasks.filter((t) => {
    switch (t.type) {
      case "project":
        if (
          filterOption.name?.projectIds &&
          filterOption.name.projectIds.length > 0
        ) {
          if (!filterOption.name.projectIds.includes(t.id)) return false;
        }
        break;
      case "task": {
        // name
        if (filterOption.name) {
          if (filterOption.name.projectIds && filterOption.name.projectIds.length > 0) {
            if (!t.project) return false;
            else if (!filterOption.name.projectIds.includes(t.project))
              return false;
          }
          if (filterOption.name.name) {
            if (t.name.indexOf(filterOption.name.name) === -1) return false;
          }
        }
        // datetime
        const datetimeFilter = filterDateProperties.some((prop) => {
          const datetimeFilterOption = filterOption[prop];
          const taskDatetime = t[prop];
          if (datetimeFilterOption !== null) {
            if (taskDatetime === null) return true;
            const { from, to } = datetimeFilterOption;
            if (from !== null) {
              if (from.getTime() > taskDatetime.getTime()) return true;
            }
            if (to !== null) {
              if (to.getTime() < taskDatetime.getTime()) return true;
            }
          }
          return false;
        });
        if (datetimeFilter) return false;
        //resource
        if (filterOption.resource) {
          if (
            !filterOption.resource.ids.some((id) => {
              if (t.resource.map((r) => r.resourceId).includes(id)) return true;
              else return false;
            })
          )
            return false;
        }
        break;
      }
      case "milestone":
      default:
        break;
    }
    return true;
  });
  return filteredTasks;
};

/**
 * タスクをソートの状態によってソートする関数
 *
 * @param tasks - タスクのリスト
 * @param sortState - ソートの状態
 * @returns ソートされたタスクのリスト
 */
export const sortTasksBySortState = (
  tasks: Task[],
  sortState: SortState,
): Task[] => {
  const { name, type } = sortState;
  const newTasks = [...tasks];
  switch (type) {
    case "up":
      newTasks.sort((a, b) => {
        let valueA = a[name];
        let valueB = b[name];
        valueA = isString(valueA) ? valueA.toLowerCase() : valueA;
        valueB = isString(valueB) ? valueB.toLowerCase() : valueB;
        if (valueA === valueB) return 0;
        if (valueA === null || isUndefined(valueA)) return 1;
        if (valueB === null || isUndefined(valueB)) return -1;
        if (valueA < valueB) return -1;
        if (valueB < valueA) return 1;
        return 0;
      });
      break;
    case "down":
      newTasks.sort((a, b) => {
        let valueA = a[name];
        let valueB = b[name];
        valueA = isString(valueA) ? valueA.toLowerCase() : valueA;
        valueB = isString(valueB) ? valueB.toLowerCase() : valueB;
        if (valueA === valueB) return 0;
        if (valueA === null || isUndefined(valueA)) return 1;
        if (valueB === null || isUndefined(valueB)) return -1;
        if (valueA > valueB) return -1;
        if (valueB > valueA) return 1;
        return 0;
      });
      break;
    case "neutral":
      break;
    default:
      throw Error("Unknown sort type");
  }
  return newTasks;
};

/**
 * 与えられたタスクの配列をプロジェクトタスクが、プロジェクトの下にくるように配置し、
 * 配置しなおしたタスクの配列を返す関数。
 *
 * @param tasks - タスクの配列
 * @returns ソートされたタスクの配列（新しい配列）
 */
const sortProjectTasksUnderProject = (tasks: Task[]): Task[] => {
  const result: Task[] = [];
  const visited = new Set<string>();

  // Helper function to recursively add project and its children
  const addProjectWithChildren = (projectId: string | undefined, depth: number = 0) => {
    if (depth > 10) return; // Prevent infinite recursion

    // First, add all projects at this level (sorted by displayOrder)
    const projectsAtLevel = tasks
      .filter(t => t.project === projectId && t.type === 'project' && !visited.has(t.id))
      .sort((a, b) => {
        const orderA = isUndefined(a.displayOrder) ? Number.MAX_VALUE : a.displayOrder;
        const orderB = isUndefined(b.displayOrder) ? Number.MAX_VALUE : b.displayOrder;
        return orderA - orderB;
      });

    for (const project of projectsAtLevel) {
      if (!visited.has(project.id)) {
        visited.add(project.id);
        result.push(project);

        // Recursively add child projects
        addProjectWithChildren(project.id, depth + 1);

        // Then add non-project tasks under this project (sorted by displayOrder)
        const tasksUnderProject = tasks
          .filter(t => t.project === project.id && t.type !== 'project' && !visited.has(t.id))
          .sort((a, b) => {
            const orderA = isUndefined(a.displayOrder) ? Number.MAX_VALUE : a.displayOrder;
            const orderB = isUndefined(b.displayOrder) ? Number.MAX_VALUE : b.displayOrder;
            return orderA - orderB;
          });

        for (const task of tasksUnderProject) {
          if (!visited.has(task.id)) {
            visited.add(task.id);
            result.push(task);
          }
        }
      }
    }

    // Add non-project tasks at this level (sorted by displayOrder)
    const tasksAtLevel = tasks
      .filter(t => t.project === projectId && t.type !== 'project' && !visited.has(t.id))
      .sort((a, b) => {
        const orderA = isUndefined(a.displayOrder) ? Number.MAX_VALUE : a.displayOrder;
        const orderB = isUndefined(b.displayOrder) ? Number.MAX_VALUE : b.displayOrder;
        return orderA - orderB;
      });

    for (const task of tasksAtLevel) {
      if (!visited.has(task.id)) {
        visited.add(task.id);
        result.push(task);
      }
    }
  };

  // Start with root level (tasks with no project or project that doesn't exist)
  addProjectWithChildren(undefined);

  // Add any remaining tasks that weren't visited (orphaned tasks)
  const remainingTasks = tasks
    .filter(t => !visited.has(t.id))
    .sort((a, b) => {
      const orderA = isUndefined(a.displayOrder) ? Number.MAX_VALUE : a.displayOrder;
      const orderB = isUndefined(b.displayOrder) ? Number.MAX_VALUE : b.displayOrder;
      return orderA - orderB;
    });

  result.push(...remainingTasks);

  return result;
};

/**
 * sortStateに応じたソートと、プロジェクトタスクをプロジェクトの下に配置する関数。
 *
 * @param tasks - タスクの配列
 * @param sortState - ソートの命令
 * @returns ソートしたタスクの配列（新しい配列）
 */
export const sortDisplayTasks = (tasks: Task[], sortState: SortState) => {
  // First apply any sorting if needed
  let sortedTasks = tasks;
  if (sortState.type !== "neutral") {
    sortedTasks = sortTasksBySortState(tasks, sortState);
  }

  // Then always prioritize project hierarchy
  return sortProjectTasksUnderProject(sortedTasks);
};

/**
 * タスクをdisplayOrderの順に並び替える関数。
 * displayOrderがundefinedのものは、一番後ろになる。
 *
 * @param tasks - 並び替えるタスクの配列
 * @returns 並び替え後のタスクの配列
 */
export const sortByDisplayOrder = (tasks: Task[]) => {
  return tasks.sort((taskA, taskB) => {
    // display orderが定義されていないものは、Number.MAX_VALUEとする。(一番後ろになる)
    const orderA = isUndefined(taskA.displayOrder)
      ? Number.MAX_VALUE
      : taskA.displayOrder;
    const orderB = isUndefined(taskB.displayOrder)
      ? Number.MAX_VALUE
      : taskB.displayOrder;
    if (orderA > orderB) {
      return 1;
    } else if (orderA < orderB) {
      return -1;
    } else {
      return 0;
    }
  });
};

/**
 * 与えられたタスクの全ての親プロジェクトを与えられたタスク配列から取得する関数。
 * 孫 → 子 → 親の順にリストに格納される。
 *
 * @param targetTask - 対象のタスク
 * @param tasks - 探索範囲となるタスク配列
 * @returns 全ての親プロジェクトのIDの配列（孫 → 子 → 親の順にリストに格納）
 */
export const getAllParentProject = (targetTask: Task, tasks: Task[]) => {
  const projectIds: string[] = [];
  let projectId = targetTask.project;
  while (projectId) {
    projectIds.push(projectId);
    const project = tasks.find((t) => t.id === projectId);
    if (project) {
      projectId = project.project;
    } else break;
  }
  return projectIds;
};

/**
 * 文字列の中からURLがあった場合、それをリンクに変える関数。
 *
 * @param text - 文字列
 * @returns URLをリンクに変換した文字列
 */
export const convertToLink = (text: string) => {
  const exp =
    /(\b(https?|ftp|file):\/\/[-A-Z0-9+&@#/%?=~_|!:,.;]*[-A-Z0-9+&@#/%=~_|])/gi;
  const text1 = text.replace(exp, "<a href='$1' target='_blanc'>$1</a>");
  const exp2 = /(^|[^/])(www\.[\S]+(\b|$))/gim;
  return text1.replace(exp2, '$1<a  href="http://$2" target="_blanc">$2</a>');
};

/**
 * タスクのプロパティ名を日本語に変換する関数。
 *
 * @param property - タスクのプロパティ名
 * @returns 日本語名
 */
export const convertTaskPropertyToJP = (property: keyof Task): string => {
  switch (property) {
    case "multiSelected":
      return "🗹";
    case "name":
      return "名称";
    case "start":
      return "開始";
    case "end":
      return "終了";
    case "time":
      return "所要時間";
    case "value":
      return "優先度";
    case "resource":
      return "リソース";
    case "temporalResource":
      return "期間付きリソース";
    case "startNoEarlierThan":
      return "指定日以後開始";
    case "deadline":
      return "締切";
    case "description":
      return "説明";
    case "fixed":
      return "固定";
    case "progress":
      return "進捗";
    default:
      return "undefined";
  }
};

/**
 * リソースのプロパティ名を日本語に変換する関数。
 *
 * @param property - リソースのプロパティ名
 * @returns 日本語名
 */
export const convertResourcePropertyToJP = (
  property: keyof Resource,
): string => {
  switch (property) {
    case "id":
      return "ID";
    case "name":
      return "名称";
    case "quantity":
      return "総数";
    case "crudType":
    case "idInDB":
    default:
      return "undefined";
  }
};

/**
 * 期間付きリソースのプロパティ名を日本語に変換する関数。
 *
 * @param property - 期間付きリソースのプロパティ名
 * @returns 日本語名
 */
export const convertTemporalResourcePropertyToJP = (
  property: keyof TemporalResource,
): string => {
  switch (property) {
    case "id":
      return "ID";
    case "name":
      return "名称";
    case "crudType":
    case "idInDB":
    default:
      return "undefined";
  }
};

/**
 * 与えられたsourceのプロパティをtargetのプロパティで上書きする関数。
 * 但し、targetのプロパティのうちundefinedのものは、上書きしない。
 *
 * @param source - 変更対象のオブジェクト
 * @param target - 変更先のオブジェクト
 * @returns 変更後のオブジェクト
 */
export const overwriteProperties = <T>(source: T, target: T): T => {
  const newSource = { ...source };
  for (const key in newSource) {
    if (!isUndefined(target[key])) newSource[key] = target[key];
  }
  return newSource;
};

// reference https://flatuicolors.com/palette/defo
// export const colorChoices: readonly string[] = [
//   "#3498db",
//   "#1abc9c",
//   "#2ecc71",
//   "#9b59b6",
//   "#f1c40f",
//   "#e67e22",
//   "#e74c3c",
//   "#34495e",
// ];

// export const selectedColorChoices: readonly string[] = [
//   "#2980b9",
//   "#16a085",
//   "#27ae60",
//   "#8e44ad",
//   "#f39c12",
//   "#d35400",
//   "#c0392b",
//   "#2c3e50",
// ];

/**
 * 選択できる色のリスト
 */
export const colorList: readonly string[] = [
  "Blue",
  "Green",
  "Emerald",
  "Violet",
  "Yellow",
  "Orange",
  "Red",
  "Navy",
  "Gray",
];

/**
 * 色の名称に応じた色（HEX）を返す関数。
 *
 * タスクの背景色や進捗の色に使用する。
 *
 * @param color - 色の名称
 * @param type - 色のタイプ
 * @returns 色（HEX）
 *
 * @example
 * ```tsx
 * let task: Task = {...}
 *
 * task = {
 *   ...task,
 *   styles: {
 *     backgroundColor: pickColor(colorName, "light"),
 *     backgroundSelectedColor: pickColor(colorName, "medium"),
 *     progressColor: pickColor(colorName, "deep"),
 *     progressSelectedColor: pickColor(colorName, "dark"),
 *   }
 * }
 * ```
 */
export const pickColor = (
  color: string,
  type: "light" | "medium" | "deep" | "dark",
): string => {
  let colors: string[];
  switch (color) {
    case "Green":
      colors = ["#1ABC9C", "#16A085", "#13856E", "#0F6957"];
      break;
    case "Emerald":
      colors = ["#2ECC71", "#27AE60", "#1E8F4D", "#16703C"];
      break;
    case "Blue":
      colors = ["#3498DB", "#2980B9", "#1F6899", "#165178"];
      break;
    case "Violet":
      colors = ["#9B59B6", "#8E44AD", "#8030A6", "#731E9E"];
      break;
    case "Navy":
      colors = ["#3C556E", "#34495E", "#2C3E50", "#233140"];
      break;
    case "Yellow":
      colors = ["#F1C40F", "#F2B010", "#F39D13", "#F38816"];
      break;
    case "Orange":
      colors = ["#E67E22", "#DC6C16", "#D35A0A", "#CC4B00"];
      break;
    case "Red":
      colors = ["#E74C3C", "#C0392B", "#96271B", "#6E190F"];
      break;
    case "Gray":
      colors = ["#95A5A6", "#7F8C8D", "#677273", "#505959"];
      break;
    case "Default Project":
      colors = ["#fac465", "#f7bb53", "#7db59a", "#59a985"];
      break;
    case "Default":
    default:
      colors = ["#6b7280", "#4b5563", "#a3a3ff", "#8282f5"];
      break;
  }
  switch (type) {
    case "light":
      return colors[0];
    case "medium":
      return colors[1];
    case "deep":
      return colors[2];
    case "dark":
      return colors[3];
  }
};

/**
 * 開始から終了までの数値を指定した間隔で増やしていったときの
 * 全ての数字をリストに格納したものを返す関数。
 *
 * pythonのrange関数に似せて作成した。
 *
 * @param start - 開始
 * @param end - 終了
 * @param step - 間隔（マイナスの場合、pythonと挙動が違うので注意）
 * @returns 数値のリスト
 *
 * @example
 * ```ts
 * range(0, 5)
 * // -> [0, 1, 2, 3, 4]
 * range(0, 5, 2)
 * // -> [0, 2, 4]
 *
 * // stepがマイナスの場合は、想定していないが、結果は以下のようになる。
 * // pythonと挙動が違うことに注意
 * range(5, 0, -1)
 * // -> [5, 4, 3, 2, 1, 0]
 * ```
 */
export const range = (
  start: number,
  end: number,
  step: number = 1,
): number[] => {
  const length = Math.floor((end - start) / step + 1);
  if (length < 0) {
    return [];
  }
  const array = new Array(length);
  for (let i = 0; i < length; i++) array[i] = start + i * step;
  return array;
};

/**
 * 与えられたリストのstartIndexのものを、endIndexに挿入する関数。
 *
 * @param list - 並び替えるリスト
 * @param startIndex - 並び替える対象のインデックス
 * @param endIndex - 挿入する位置のインデックス
 * @returns 並び替え後のリスト
 */
export const reorder = <T>(
  list: Array<T>,
  startIndex: number,
  endIndex: number,
) => {
  const result = Array.from(list);
  const [removed] = result.splice(startIndex, 1);
  result.splice(endIndex, 0, removed);

  return result;
};
