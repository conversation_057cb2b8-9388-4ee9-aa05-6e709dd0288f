import { CircularProgressbar } from "react-circular-progressbar";
import "react-circular-progressbar/dist/styles.css";

/**
 * {@link RateCircle}のprops
 */
export interface RateCircleProps {
  /** 分子 */
  numerator: number;
  /** 分母 */
  denominator: number;
  /** CircularProgressbarに渡すクラス名 */
  className?: string;
}

/**
 * 指定した割合の円グラフを表示するコンポーネント。
 */
const RateCircle: React.FC<RateCircleProps> = ({
  numerator,
  denominator,
  className,
}) => {
  // percetageを計算。分母が0になるものは、0%とする。
  const percentage =
    denominator !== 0 ? Math.round((numerator / denominator) * 100) : 0;

  // percentageによって色を決める
  let color = "black";
  if (percentage <= 40) color = "#4054CA";
  else if (percentage <= 60) color = "#44D29E";
  else if (percentage <= 80) color = "#D9E03F";
  else if (percentage <= 100) color = "#ED7922";
  else color = "#CA3259";

  return (
    <CircularProgressbar
      className={className}
      value={percentage}
      text={`${percentage}%`}
      styles={{
        path: { stroke: color, strokeWidth: 11 },
        trail: { strokeWidth: 11 },
        text: { fill: "black", fontSize: 28, fontWeight: 700 },
      }}
    />
  );
};

export default RateCircle;
