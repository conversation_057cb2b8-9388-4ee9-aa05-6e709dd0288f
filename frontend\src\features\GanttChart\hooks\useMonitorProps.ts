import { useRef } from 'react';
import { useShallow } from "zustand/react/shallow";
import { useStylingOptionStore } from '@/stores/stylingOption';
import { useResourceListColumns } from '@/features/GanttChart/hooks/useResourceListColumns';
import { useTemporalResourceListColumns } from '@/features/GanttChart/hooks/useTemporalResourceListColumns';
import { useResize } from '@/features/GanttChart/hooks/useResize';
import { useTaskListStore } from '@/stores/taskLists';
import { useGanttSchedulerStore } from '@/stores/ganttScheduler';
import { useNonDeletedTask } from '@/stores/hooks/ganttScheduler';
import { newGanttDateRange, seedDates } from '@/common/helpers/date-helper';

/**
 * Hook that provides all the props needed for ResourceMonitor and TemporalResourceMonitor
 * This centralizes the prop calculation logic so it can be reused in both the main interface and popups
 */
export const useMonitorProps = () => {
  // Basic configuration
  const viewMode = useStylingOptionStore(state => state.viewMode);
  const headerHeight = 50;
  const columnWidth = 60;
  const rowHeight = 50;
  const gripAreaWidth = 0;
  const locale = "ja-JP";
  const fontFamily = "Arial, Roboto, Oxygen, Ubuntu, Cantarell, Fira Sans, Droid Sans, Helvetica Neue";
  const fontSize = "14px";
  const todayColor = "rgba(252, 248, 227, 0.5)";
  const preStepsCount = 1; // For date range calculation

  // Get actual data from gantt scheduler store
  const tasks = useNonDeletedTask();
  const scheduleDay = useGanttSchedulerStore(state => state.scheduleDay);

  // Task list width calculation
  const taskListFullWidth = useTaskListStore(
    useShallow((state) => {
      let totalWidth = 0;
      state.columns.forEach((col) => {
        totalWidth += col.width;
      });
      totalWidth += gripAreaWidth;
      return totalWidth;
    }),
  );

  // Column configurations
  const { resourceColumns, changeColumnWidth, totalColumnWidth } = useResourceListColumns([
    { name: "name", width: 100 },
    { name: "quantity", width: 100 },
  ]);
  
  const {
    temporalResourceColumns,
    changeColumnWidth: changeTemporalColumnWidth,
    totalColumnWidth: totalTemporalColumnWidth,
  } = useTemporalResourceListColumns([{ name: "name", width: 100 }]);

  // Width calculations
  const listFullWidth = Math.max(
    totalColumnWidth + gripAreaWidth,
    totalTemporalColumnWidth + gripAreaWidth,
    taskListFullWidth,
  );
  
  const { size: listWidth, handleChangeSize: handleChangeListWidth } = useResize(
    310, 
    0, 
    listFullWidth, 
    "task-list-resource-table-width"
  );

  // Refs for monitors
  const resourceGridRef = useRef<HTMLDivElement>(null);
  const temporalResourceGridRef = useRef<HTMLDivElement>(null);

  // Table widths
  const tableWidth = totalTemporalColumnWidth + gripAreaWidth;
  const resourceTableWidth = totalColumnWidth + gripAreaWidth;

  // Calendar and SVG setup - use real date calculation like the main Gantt component
  const [startDate, endDate] = newGanttDateRange(
    tasks,
    viewMode,
    preStepsCount,
    scheduleDay.start,
    scheduleDay.end,
  );
  const dates = seedDates(startDate, endDate, viewMode);
  const dateSetup = { dates, viewMode };
  const svgWidth = dates.length * columnWidth;
  
  const calendarProps = {
    dateSetup,
    locale,
    headerHeight,
    columnWidth,
    fontFamily,
    fontSize,
    rtl: false,
  };

  // Horizontal resizer props
  const horizontalResizerProps = { 
    size: listWidth, 
    handleChangeSize: handleChangeListWidth 
  };

  // Scroll handling
  const handleScrollX = () => {};
  const scrollX = 0;

  // Return all props needed for both monitors
  return {
    // Resource Monitor Props
    resourceMonitorProps: {
      resourceColumns,
      changeColumnWidth,
      listWidth,
      svgWidth,
      headerHeight,
      rowHeight,
      columnWidth,
      gripAreaWidth,
      todayColor,
      calendarProps,
      scrollX,
      horizontalResizerProps,
      resourceGridRef,
      resourceTableWidth,
      handleScrollX,
    },
    
    // Temporal Resource Monitor Props
    temporalResourceMonitorProps: {
      listColumns: temporalResourceColumns,
      changeColumnWidth: changeTemporalColumnWidth,
      listWidth,
      svgWidth,
      headerHeight,
      rowHeight,
      columnWidth,
      gripAreaWidth,
      todayColor,
      calendarProps,
      scrollX,
      horizontalResizerProps,
      gridRef: temporalResourceGridRef,
      tableWidth,
      handleScrollX,
    },
  };
}; 