import { useEffect, useState } from 'react';
import { createPortal } from 'react-dom';
import { Button } from '@/components/ui/button';
import { ExternalLink } from 'lucide-react';

interface PopOutWindowProps {
  children: React.ReactNode;
  title: string;
  width?: number;
  height?: number;
  onClose?: () => void;
  features?: string[];
  className?: string;
  monitorType?: string;
}

/**
 * Component that renders its children in a pop-out window
 * Uses window.open() and portals content into the new window
 */
const PopOutWindow = ({
  children,
  title,
  width = 800,
  height = 600,
  onClose,
  features = [],
  className = '',
  syncState = false,
}: PopOutWindowProps) => {
  const [externalWindow, setExternalWindow] = useState<Window | null>(null);
  
  useEffect(() => {
    // Default features
    const defaultFeatures = [
      'popup=true',
      'menubar=no',
      'toolbar=no',
      'location=no',
      'status=no',
      `width=${width}`,
      `height=${height}`,
      'resizable=yes',
      'scrollbars=yes',
    ];
    
    // Combine with custom features
    const windowFeatures = [...defaultFeatures, ...features].join(',');
    
    // Open the new window
    const newWindow = window.open('', title, windowFeatures);
    
    if (newWindow) {
      // Set basic HTML structure for the new window
      newWindow.document.title = title;
      
      // Copy over CSS from main window
      const mainStylesheets = document.querySelectorAll('link[rel="stylesheet"]');
      mainStylesheets.forEach(stylesheet => {
        const newStylesheet = newWindow.document.createElement('link');
        newStylesheet.rel = 'stylesheet';
        newStylesheet.href = (stylesheet as HTMLLinkElement).href;
        newWindow.document.head.appendChild(newStylesheet);
      });
      
      // Add custom styling
      const style = newWindow.document.createElement('style');
      style.textContent = `
        body {
          margin: 0;
          padding: 0;
          font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif;
          background-color: var(--background);
          color: var(--foreground);
          overflow: hidden;
        }
        #root {
          height: 100vh;
          overflow: hidden;
          position: relative;
        }
        .pop-out-close-button {
          position: absolute;
          top: 8px;
          right: 8px;
          z-index: 1000;
          background-color: var(--primary);
          color: var(--primary-foreground);
          border: none;
          border-radius: 4px;
          width: 28px;
          height: 28px;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          opacity: 0.7;
          transition: opacity 0.2s;
        }
        .pop-out-close-button:hover {
          opacity: 1;
        }
        .${className} {
          height: 100% !important;
          width: 100% !important;
          max-height: none !important;
        }
      `;
      newWindow.document.head.appendChild(style);
      
      // Create a container for our React content
      const container = newWindow.document.createElement('div');
      container.id = 'root';
      container.className = className;
      newWindow.document.body.appendChild(container);
      
      // Add close button
      const closeButton = newWindow.document.createElement('button');
      closeButton.className = 'pop-out-close-button';
      closeButton.title = 'Close window';
      closeButton.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="18" y1="6" x2="6" y2="18"></line><line x1="6" y1="6" x2="18" y2="18"></line></svg>';
      closeButton.onclick = () => {
        newWindow.close();
      };
      container.appendChild(closeButton);
      
      // Set window reference
      setExternalWindow(newWindow);
      
      // Sync theme if necessary
      if (document.documentElement.classList.contains('dark')) {
        newWindow.document.documentElement.classList.add('dark');
      }
      
      // Set up event listener for window close
      newWindow.addEventListener('beforeunload', () => {
        if (onClose) onClose();
      });
      
      // Clean up when component unmounts
      return () => {
        newWindow.close();
        if (onClose) onClose();
      };
    }
  }, [title, width, height, features, onClose, className, syncState]);
  
  // Portal our children into the external window when it's available
  return externalWindow ? createPortal(children, externalWindow.document.getElementById('root')!) : null;
};

/**
 * Button that opens content in a pop-out window when clicked
 */
export const PopOutButton = ({
  children,
  title,
  width,
  height,
  features,
  className,
  syncState,
}: Omit<PopOutWindowProps, 'onClose'>) => {
  const [isOpen, setIsOpen] = useState(false);
  
  return (
    <>
      <Button
        variant="ghost"
        size="sm"
        className="p-1"
        onClick={() => setIsOpen(true)}
        title="Pop out to separate window"
      >
        <ExternalLink className="h-4 w-4" />
      </Button>
      
      {isOpen && (
        <PopOutWindow
          title={title}
          width={width}
          height={height}
          features={features}
          className={className}
          syncState={syncState}
          onClose={() => setIsOpen(false)}
        >
          {children}
        </PopOutWindow>
      )}
    </>
  );
};

export default PopOutWindow;
