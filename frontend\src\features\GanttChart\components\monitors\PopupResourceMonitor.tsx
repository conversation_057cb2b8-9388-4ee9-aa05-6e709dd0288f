import React, { SyntheticEvent, useEffect, useRef, useState } from "react";
import { useVirtualizer } from "@tanstack/react-virtual";
import { Cog8ToothIcon } from "@heroicons/react/24/outline";

import { Calendar, CalendarProps } from "@/features/GanttChart/components/calendar/Calendar";
import HorizontalResizer, {
  HorizontalResizerProps,
} from "@/features/GanttChart/components/other/HorizontalResizer";
import { ResourceColumn } from "@/features/GanttChart/hooks/useResourceListColumns";
import { useScroll } from "@/features/GanttChart/hooks/useScroll";

import ResourceTable from "./resource-monitor/ResourceTable";
import ResourceGrid from "./resource-monitor/ResourceGrid";
import { HorizontalScroll } from "@/features/GanttChart/components/other/HorizontalScroll";
import ResourceMonitorSetting from "./resource-monitor/ResourceMonitorSetting";
import { useDisplayResource } from "@/features/GanttChart/hooks/useDisplayResource";
import { Dialog, DialogContent, DialogTrigger } from "@/components/ui/dialog";

/** Props for PopupResourceMonitor - same as ResourceMonitor but no state check */
export interface PopupResourceMonitorProps {
  resourceColumns: ResourceColumn[];
  changeColumnWidth: (name: string, width: number) => void;
  listWidth: number;
  svgWidth: number;
  headerHeight: number;
  rowHeight: number;
  columnWidth: number;
  gripAreaWidth: number;
  scrollX: number;
  todayColor: string;
  calendarProps: Omit<CalendarProps, "columnVirtualizer">;
  horizontalResizerProps: HorizontalResizerProps;
  resourceGridRef: React.RefObject<HTMLDivElement | null>;
  resourceTableWidth: number;
  handleScrollX: (event: SyntheticEvent<HTMLDivElement>) => void;
}

export const PopupResourceMonitor: React.FC<PopupResourceMonitorProps> = ({
  resourceColumns,
  changeColumnWidth,
  listWidth,
  svgWidth,
  headerHeight,
  rowHeight,
  columnWidth,
  gripAreaWidth,
  todayColor,
  calendarProps,
  scrollX,
  horizontalResizerProps,
  resourceGridRef,
  resourceTableWidth,
  handleScrollX,
}) => {
  const resourceMonitorRef = useRef<HTMLDivElement>(null);
  const [isSettingsOpen, setIsSettingsOpen] = useState(false);

  const { resource, isDisplayed } = useDisplayResource();
  const monitorFullHeight = rowHeight * resource.length + headerHeight;

  const { scroll: scrollY, handleScroll: handleScrollY } = useScroll({
    ref: resourceMonitorRef as React.RefObject<HTMLElement>,
    type: "vertical",
    max: monitorFullHeight,
  });
  const { scroll: resourceTableScrollX, handleScroll: handleResourceTableScrollX } = useScroll({
    ref: resourceMonitorRef as React.RefObject<HTMLElement>,
    type: "horizontal",
    max: resourceTableWidth,
  });

  const columnVirtualizer = useVirtualizer({
    count: calendarProps.dateSetup.dates.length,
    getScrollElement: () => resourceGridRef.current,
    estimateSize: () => columnWidth,
    horizontal: true,
  });

  useEffect(() => {
    if (resourceGridRef.current) {
      resourceGridRef.current.scrollLeft = scrollX;
    }
  }, [scrollX]);

  // Always render in popup - no state check

  const headerContent = (
    <div className="flex">
      <div
        style={{
          width: listWidth,
          height: headerHeight,
        }}
        className="flex-shrink-0 bg-background border-b border-r border-gray-200 dark:border-gray-700 flex items-center justify-center"
      >
        <span className="text-xs font-medium text-gray-500 dark:text-gray-400">
          Resources
        </span>
      </div>
      <div className="flex-1 bg-background border-b border-gray-200 dark:border-gray-700 flex items-center justify-end pr-8">
        <Dialog open={isSettingsOpen} onOpenChange={setIsSettingsOpen}>
          <DialogTrigger asChild>
            <button
              className="p-2 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-md transition-colors"
              title="Resource Monitor Settings"
            >
              <Cog8ToothIcon className="h-4 w-4 text-gray-500 dark:text-gray-400" />
            </button>
          </DialogTrigger>
          <DialogContent className="max-w-md">
            <ResourceMonitorSetting />
          </DialogContent>
        </Dialog>
      </div>
    </div>
  );

  const mainContent = isDisplayed ? (
    <>
      <div className="flex flex-1 overflow-y-auto overflow-x-hidden">        <div
          className="relative m-0 flex overflow-hidden p-0 outline-0 w-full"
        >
          <div
            style={{
              minWidth: `${listWidth}px`,
              maxWidth: `${listWidth}px`,
            }}
          >
            <ResourceTable
              resources={resource}
              listWidth={listWidth}
              rowHeight={rowHeight}
              headerHeight={headerHeight}
              contentHeight={600} // Fixed height for popup
              gripAreaWidth={gripAreaWidth}
              scrollX={resourceTableScrollX}
              scrollY={scrollY}
              resourceColumns={resourceColumns}
              changeColumnWidth={changeColumnWidth}
            />
          </div>

          <HorizontalResizer {...horizontalResizerProps} />

          <div
            className="hidden-scrollbar m-0 overflow-x-scroll p-0"
            ref={resourceGridRef}
            dir="ltr"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width={svgWidth}
              height={calendarProps.headerHeight}
            >
              <Calendar
                {...calendarProps}
                columnVirtualizer={columnVirtualizer}
              />
            </svg>
            <ResourceGrid
              dates={calendarProps.dateSetup.dates}
              resources={resource}
              svgWidth={svgWidth}
              rowHeight={rowHeight}
              columnWidth={columnWidth}
              contentHeight={600} // Fixed height for popup
              todayColor={todayColor}
              scrollY={scrollY}
              columnVirtualizer={columnVirtualizer}
            />
          </div>
        </div>

        <div className="flex">
          <HorizontalScroll
            svgWidth={resourceTableWidth}
            minWidth={listWidth}
            scroll={resourceTableScrollX}
            onScroll={handleResourceTableScrollX}
          />
          <HorizontalScroll
            svgWidth={svgWidth}
            scroll={scrollX}
            onScroll={handleScrollX}
          />
        </div>
      </div>
    </>
  ) : null;

  return (
    <div
      ref={resourceMonitorRef}
      className="flex flex-col h-full w-full"
      style={{ height: "100%" }}
    >
      {headerContent}
      {mainContent}
    </div>
  );
};

export default PopupResourceMonitor; 