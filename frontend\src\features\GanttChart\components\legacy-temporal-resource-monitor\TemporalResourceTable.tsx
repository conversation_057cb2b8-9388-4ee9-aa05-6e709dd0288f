import { memo, useEffect, useRef, useState } from "react";
import { css } from "@emotion/react";
import {
  closestCenter,
  DndContext,
  DragOverlay,
  PointerSensor,
  useSensor,
  useSensors,
} from "@dnd-kit/core";
import { restrictToVerticalAxis } from "@dnd-kit/modifiers";
import {
  SortableContext,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable";

import { TemporalResource } from "@/common/types";

import { useResourceMonitorStore } from "@/stores/resourceMonitor";

import TemporalResourceTableHeader from "./TemporalResourceTableHeader";
import SortableTemporalResourceTableRow from "./SortableTemporalResourceTableRow";
import { TemporalResourceColumn } from "../../hooks/useTemporalResourceListColumns";
import TemporalResourceTableRow from "./TemporalResourceTableRow";

/**
 * {@link TemporalResourceTable}のprops
 */
export interface TemporalResourceTableProps {
  /** 表示する期間付きリソース */
  resources: TemporalResource[];
  /** 表の表示幅 */
  listWidth: number;
  /** 行の高さ */
  rowHeight: number;
  /** ヘッダの高さ */
  headerHeight: number;
  /** 表の高さ */
  contentHeight: number;
  /** Tableの並び替えを行うエリアの幅 */
  gripAreaWidth: number;
  /** 表のX方向のスクロール量 */
  scrollX: number;
  /** 表のY方向のスクロール量 */
  scrollY: number;
  /** 表に表示する項目の情報 */
  listColumns: TemporalResourceColumn[];
  /**
   * 項目の表示幅を変更する関数
   * @param name - 項目のプロパティ名
   * @param width - 新しい幅
   */
  changeColumnWidth: (name: string, width: number) => void;
}

/**
 * リソースモニターの左側に表示する表
 */
const TemporalResourceTable: React.FC<TemporalResourceTableProps> = memo(
  ({
    resources,
    listWidth,
    rowHeight,
    headerHeight,
    contentHeight,
    gripAreaWidth,
    scrollX,
    scrollY,
    listColumns,
    changeColumnWidth,
  }) => {
    const reorderResource =
      useResourceMonitorStore.use.reorderTemporalResource();

    // local state
    const [activeResource, setActiveResource] =
      useState<TemporalResource | null>(null);

    const sensors = useSensors(useSensor(PointerSensor));

    const resourceTableRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
      if (resourceTableRef.current !== null) {
        resourceTableRef.current.scrollLeft = scrollX;
      }
    }, [scrollX]);

    useEffect(() => {
      if (resourceTableRef.current !== null) {
        resourceTableRef.current.scrollTop = scrollY;
      }
    }, [scrollY]);

    return (
      <div>
        <TemporalResourceTableHeader
          headerHeight={headerHeight}
          gripAreaWidth={gripAreaWidth}
          listColumns={listColumns}
          changeColumnWidth={changeColumnWidth}
          scrollX={scrollX}
        />
        <div
          className="hidden-scrollbar m-0 overflow-x-hidden bg-white p-0"
          css={
            contentHeight
              ? css`
                  height: ${contentHeight}px;
                `
              : ""
          }
          ref={resourceTableRef}
        >
          <div className="table w-full border-b border-l border-gray-300">
            <DndContext
              sensors={sensors}
              collisionDetection={closestCenter}
              modifiers={[restrictToVerticalAxis]}
              onDragStart={(event) => {
                setActiveResource((prev) => {
                  if (prev === null || prev.id !== event.active.id) {
                    const newResource = resources.find(
                      (r) => r.id === event.active.id,
                    );
                    return newResource ?? null;
                  } else return prev;
                });
              }}
              onDragCancel={() => {
                setActiveResource(null);
              }}
              onDragEnd={(event) => {
                const { active, over } = event;
                if (over !== null && active.id !== over.id) {
                  const activeIndex = resources.findIndex(
                    (r) => r.id === active.id,
                  );
                  const overIndex = resources.findIndex(
                    (r) => r.id === over.id,
                  );
                  if (activeIndex !== -1 && overIndex !== -1) {
                    reorderResource(activeIndex, overIndex);
                  }
                }
                setActiveResource(null);
              }}
            >
              <SortableContext
                items={resources}
                strategy={verticalListSortingStrategy}
              >
                {resources.map((resource, index) => (
                  <SortableTemporalResourceTableRow
                    key={resource.id}
                    index={index}
                    resource={resource}
                    listWidth={listWidth}
                    rowHeight={rowHeight}
                    gripAreaWidth={gripAreaWidth}
                    listColumns={listColumns}
                  />
                ))}
              </SortableContext>
              <DragOverlay>
                {activeResource !== null && (
                  <TemporalResourceTableRow
                    index={0}
                    resource={activeResource}
                    listWidth={listWidth}
                    rowHeight={rowHeight}
                    gripAreaWidth={gripAreaWidth}
                    listColumns={listColumns}
                  />
                )}
              </DragOverlay>
            </DndContext>
          </div>
        </div>
      </div>
    );
  },
);

export default TemporalResourceTable;
