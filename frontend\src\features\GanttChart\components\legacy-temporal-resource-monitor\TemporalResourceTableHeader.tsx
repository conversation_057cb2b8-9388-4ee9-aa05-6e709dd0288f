import { useEffect, useRef } from "react";
import { css } from "@emotion/react";

import { convertTemporalResourcePropertyToJP } from "@/common/helpers/other-helper";
import HeaderColumnWidthResizer from "../task-list/HeaderColumnWidthResizer";
import { TemporalResourceColumn } from "../../hooks/useTemporalResourceListColumns";

/**
 * {@link TemporalResourceTableHeader}のprops
 */
export interface TemporalResourceTableHeaderProps {
  /** ヘッダの高さ */
  headerHeight: number;
  /** Tableの並び替えを行うエリアの幅 */
  gripAreaWidth: number;
  /** 表示する項目の情報 */
  listColumns: TemporalResourceColumn[];
  /**
   * 項目の表示幅を変更する関数
   * @param name - 項目のプロパティ名
   * @param width - 新しい幅
   */
  changeColumnWidth: (name: string, width: number) => void;
  /** TableのX方向のスクロール量 */
  scrollX: number;
}

const TemporalResourceTableHeader: React.FC<
  TemporalResourceTableHeaderProps
> = ({
  headerHeight,
  gripAreaWidth,
  listColumns,
  changeColumnWidth,
  scrollX,
}) => {
  const resourceTableHeaderRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (resourceTableHeaderRef.current !== null)
      resourceTableHeaderRef.current.scrollLeft = scrollX;
  }, [scrollX]);

  return (
    <div
      css={css`
        min-height: ${headerHeight}px;
      `}
      className="m-0 flex overflow-hidden border-y border-l border-solid border-gray-300 bg-white p-0 text-sm font-medium"
      ref={resourceTableHeaderRef}
    >
      <div
        css={css`
          margin-left: ${gripAreaWidth}px;
        `}
      ></div>
      {listColumns.map((col) => (
        <div
          key={col.name}
          css={css`
            min-width: ${col.width}px;
            max-width: ${col.width}px;
          `}
          className="flex items-center justify-between pl-4"
        >
          {convertTemporalResourcePropertyToJP(col.name)}
          <HeaderColumnWidthResizer
            width={11}
            columnName={col.name}
            columnWidth={col.width}
            headerHeight={headerHeight}
            changeColumnWidth={changeColumnWidth}
            changeColumnWidthGlobal={() => {}}
          />
        </div>
      ))}
    </div>
  );
};

export default TemporalResourceTableHeader;
