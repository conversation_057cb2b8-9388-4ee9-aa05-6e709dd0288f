import { useEffect, useRef } from "react";
import { css } from "@emotion/react";
import { useVirtualizer, Virtualizer } from "@tanstack/react-virtual";

import { TemporalResource } from "@/common/types";
import GridRows from "../grid/GridRows";
import GridColumns from "../grid/GridColumns";
import TemporalResourceGridContent from "./TemporalResourceGridContent";

/**
 * {@link TemporalResourceGrid}のprops
 */
export interface TemporalResourceGridProps {
  /** カレンダーの日付 */
  dates: Date[];
  /** 表示する期間付きリソースのリスト */
  resources: TemporalResource[];
  /** カレンダー全体のサイズ */
  svgWidth: number;
  /** 行の高さ */
  rowHeight: number;
  /** カレンダーの1つの日付の幅 */
  columnWidth: number;
  /** 表示するグリッドの高さ（カレンダーを除く） */
  contentHeight: number;
  /** カレンダーの今の部分のハイライトカラー(HEX) */
  todayColor: string;
  /** Y方向のスクロール */
  scrollY: number;
  /** 列方向の仮想スクロールハンドラ */
  columnVirtualizer?: Virtualizer<HTMLDivElement, Element>;
}

/**
 * リソースモニターのグリッド部分のコンポーネント
 */
const TemporalResourceGrid: React.FC<TemporalResourceGridProps> = ({
  dates,
  resources,
  svgWidth,
  rowHeight,
  columnWidth,
  contentHeight,
  todayColor,
  scrollY,
  columnVirtualizer,
}) => {
  const ref = useRef<HTMLDivElement>(null);

  const rowVirtualizer = useVirtualizer({
    count: resources.length,
    getScrollElement: () => ref.current,
    estimateSize: () => rowHeight,
  });

  useEffect(() => {
    if (ref.current !== null) {
      ref.current.scrollTop = scrollY;
    }
  }, [scrollY]);

  return (
    <div
      className="hidden-scrollbar m-0 overflow-y-scroll p-0"
      ref={ref}
      css={css`
        ${contentHeight ? `height: ${contentHeight}px;` : ""}
        width: ${svgWidth}px;
      `}
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width={svgWidth}
        height={rowHeight * resources.length}
      >
        <GridRows
          rows={resources}
          svgWidth={svgWidth}
          rowHeight={rowHeight}
          rowVirtualizer={rowVirtualizer}
        />
        <GridColumns
          dates={dates}
          columnWidth={columnWidth}
          columnHeight={rowHeight * resources.length}
          todayColor={todayColor}
          columnVirtualizer={columnVirtualizer}
        />
        <TemporalResourceGridContent
          dates={dates}
          resources={resources}
          columnWidth={columnWidth}
          rowHeight={rowHeight}
        />
      </svg>
    </div>
  );
};

export default TemporalResourceGrid;
