import React, { SyntheticEvent, useEffect, useRef } from "react";
import { useVirtualizer } from "@tanstack/react-virtual";

import { Calendar, CalendarProps } from "@/features/GanttChart/components/calendar/Calendar";
import { HorizontalScroll } from "@/features/GanttChart/components/other/HorizontalScroll";
import HorizontalResizer, {
  HorizontalResizerProps,
} from "@/features/GanttChart/components/other/HorizontalResizer";
import { TemporalResourceColumn } from "@/features/GanttChart/hooks/useTemporalResourceListColumns";
import { useScroll } from "@/features/GanttChart/hooks/useScroll";
import TemporalResourceTable from "./temporal-resource-monitor/TemporalResourceTable";
import TemporalResourceGrid from "./temporal-resource-monitor/TemporalResourceGrid";
import TemporalResourceMonitorSetting from "./temporal-resource-monitor/TemporalResourceMonitorSetting";
import { useDisplayTemporalResource } from "@/features/GanttChart/hooks/useDisplayTemporalResource";

export interface PopupTemporalResourceMonitorProps {
  listColumns: TemporalResourceColumn[];
  changeColumnWidth: (name: string, width: number) => void;
  listWidth: number;
  svgWidth: number;
  headerHeight: number;
  rowHeight: number;
  columnWidth: number;
  gripAreaWidth: number;
  scrollX: number;
  todayColor: string;
  calendarProps: Omit<CalendarProps, "columnVirtualizer">;
  horizontalResizerProps: HorizontalResizerProps;
  gridRef: React.RefObject<HTMLDivElement | null>;
  tableWidth: number;
  handleScrollX: (event: SyntheticEvent<HTMLDivElement>) => void;
}

export const PopupTemporalResourceMonitor: React.FC<PopupTemporalResourceMonitorProps> = ({
  listColumns,
  changeColumnWidth,
  listWidth,
  svgWidth,
  headerHeight,
  rowHeight,
  columnWidth,
  gripAreaWidth,
  todayColor,
  calendarProps,
  scrollX,
  horizontalResizerProps,
  gridRef,
  tableWidth,
  handleScrollX,
}) => {
  const temporalResourceMonitorRef = useRef<HTMLDivElement>(null);

  const { temporalResource, isDisplayed } = useDisplayTemporalResource();
  const monitorFullHeight = rowHeight * temporalResource.length + headerHeight;

  const { scroll: scrollY, handleScroll: handleScrollY } = useScroll({
    ref: temporalResourceMonitorRef as React.RefObject<HTMLElement>,
    type: "vertical",
    max: monitorFullHeight,
  });
  const { scroll: tableScrollX, handleScroll: handleTableScrollX } = useScroll({
    ref: temporalResourceMonitorRef as React.RefObject<HTMLElement>,
    type: "horizontal",
    max: tableWidth,
  });

  const columnVirtualizer = useVirtualizer({
    count: calendarProps.dateSetup.dates.length,
    getScrollElement: () => gridRef.current,
    estimateSize: () => columnWidth,
    horizontal: true,
  });

  useEffect(() => {
    if (gridRef.current) {
      gridRef.current.scrollLeft = scrollX;
    }
  }, [scrollX]);

  // Always render in popup - no state check

  const headerContent = (
    <div className="flex">
      <div
        style={{
          width: listWidth,
          height: headerHeight,
        }}
        className="flex-shrink-0 bg-background border-b border-r border-gray-200 dark:border-gray-700 flex items-center justify-center"
      >
        <span className="text-xs font-medium text-gray-500 dark:text-gray-400">
          Temporal Resources
        </span>
      </div>
      <div className="flex-1 bg-background border-b border-gray-200 dark:border-gray-700 flex items-center justify-end pr-8">
        <TemporalResourceMonitorSetting />
      </div>
    </div>
  );

  const mainContent = isDisplayed ? (
    <>
      <div className="flex flex-1 overflow-y-auto overflow-x-hidden">        <div
          className="relative m-0 flex overflow-hidden p-0 outline-0 w-full"
        >
          <div
            style={{
              minWidth: `${listWidth}px`,
              maxWidth: `${listWidth}px`,
            }}
          >
            <TemporalResourceTable
              resources={temporalResource}
              listWidth={listWidth}
              rowHeight={rowHeight}
              headerHeight={headerHeight}
              contentHeight={600} // Fixed height for popup
              gripAreaWidth={gripAreaWidth}
              scrollX={tableScrollX}
              scrollY={scrollY}
              listColumns={listColumns}
              changeColumnWidth={changeColumnWidth}
            />
          </div>

          <HorizontalResizer {...horizontalResizerProps} />

          <div
            className="hidden-scrollbar m-0 overflow-x-scroll p-0"
            ref={gridRef}
            dir="ltr"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width={svgWidth}
              height={calendarProps.headerHeight}
            >
              <Calendar
                {...calendarProps}
                columnVirtualizer={columnVirtualizer}
              />
            </svg>
            <TemporalResourceGrid
              dates={calendarProps.dateSetup.dates}
              resources={temporalResource}
              svgWidth={svgWidth}
              rowHeight={rowHeight}
              columnWidth={columnWidth}
              contentHeight={600} // Fixed height for popup
              todayColor={todayColor}
              scrollY={scrollY}
              columnVirtualizer={columnVirtualizer}
            />
          </div>
        </div>

        <div className="flex">
          <HorizontalScroll
            svgWidth={tableWidth}
            minWidth={listWidth}
            scroll={tableScrollX}
            onScroll={handleTableScrollX}
          />
          <HorizontalScroll
            svgWidth={svgWidth}
            scroll={scrollX}
            onScroll={handleScrollX}
          />
        </div>
      </div>
    </>
  ) : null;

  return (
    <div
      ref={temporalResourceMonitorRef}
      className="flex flex-col h-full w-full"
      style={{ height: "100%" }}
    >
      {headerContent}
      {mainContent}
    </div>
  );
};

export default PopupTemporalResourceMonitor; 