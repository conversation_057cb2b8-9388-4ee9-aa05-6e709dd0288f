import { memo, useEffect, useRef, useState } from "react";
import { css } from "@emotion/react";
import {
  closestCenter,
  DndContext,
  DragOverlay,
  PointerSensor,
  useSensor,
  useSensors,
} from "@dnd-kit/core";
import {
  SortableContext,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import { restrictToVerticalAxis } from "@dnd-kit/modifiers";

import { Resource } from "@/common/types";
import { useResourceMonitorStore } from "@/stores/resourceMonitor";

import { ResourceColumn } from "../../hooks/useResourceListColumns";
import ResourceTableHeader from "./ResourceTableHeader";
import SortableResourceTableRow from "./SortableResourceTableRow";
import ResourceTableRow from "./ResourceTableRow";

/**
 * {@link ResourceTable}のprops
 */
export interface ResourceTableProps {
  /** 表示するリソース */
  resources: Resource[];
  /** Tableの表示幅。（タスクリストと同期させる） */
  listWidth: number;
  /** 行の高さ */
  rowHeight: number;
  /** ヘッダの高さ */
  headerHeight: number;
  /** 表の中身の高さ */
  contentHeight: number;
  /** 表の並び替えを行うエリアの幅 */
  gripAreaWidth: number;
  /** X方向のスクロール量 */
  scrollX: number;
  /** Y方向のスクロール量 */
  scrollY: number;
  /** 表示する項目情報 */
  resourceColumns: ResourceColumn[];
  /**
   * 項目の表示幅を変更する関数
   * @param name - 項目のプロパティ名
   * @param width - 新しい表示幅
   */
  changeColumnWidth: (name: string, width: number) => void;
}

/**
 * リソースモニターの左側の一覧表
 */
const ResourceTable: React.FC<ResourceTableProps> = memo(
  ({
    resources,
    listWidth,
    rowHeight,
    headerHeight,
    contentHeight,
    gripAreaWidth,
    scrollX,
    scrollY,
    resourceColumns,
    changeColumnWidth,
  }) => {
    // global state
    const reorderResource = useResourceMonitorStore.use.reorderResource();

    // local state
    const [activeResource, setActiveResource] = useState<Resource | null>(null);

    const sensors = useSensors(useSensor(PointerSensor));

    const resourceTableRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
      if (resourceTableRef.current !== null) {
        resourceTableRef.current.scrollLeft = scrollX;
      }
    }, [scrollX]);

    useEffect(() => {
      if (resourceTableRef.current !== null) {
        resourceTableRef.current.scrollTop = scrollY;
      }
    }, [scrollY]);

    return (
      <div>
        <ResourceTableHeader
          headerHeight={headerHeight}
          gripAreaWidth={gripAreaWidth}
          resourceColumns={resourceColumns}
          changeColumnWidth={changeColumnWidth}
          scrollX={scrollX}
        />
        <div
          className="hidden-scrollbar m-0 overflow-x-hidden bg-white p-0"
          css={
            contentHeight
              ? css`
                  height: ${contentHeight}px;
                `
              : ""
          }
          ref={resourceTableRef}
        >
          <div className="table w-full border-b border-l border-gray-300">
            <DndContext
              sensors={sensors}
              collisionDetection={closestCenter}
              modifiers={[restrictToVerticalAxis]}
              onDragStart={(event) => {
                setActiveResource((prev) => {
                  if (prev === null || prev.id !== event.active.id) {
                    const newResource = resources.find(
                      (r) => r.id === event.active.id,
                    );
                    return newResource ?? null;
                  } else return prev;
                });
              }}
              onDragCancel={() => {
                setActiveResource(null);
              }}
              onDragEnd={(event) => {
                const { active, over } = event;
                if (over !== null && active.id !== over.id) {
                  const activeIndex = resources.findIndex(
                    (r) => r.id === active.id,
                  );
                  const overIndex = resources.findIndex(
                    (r) => r.id === over.id,
                  );
                  if (activeIndex !== -1 && overIndex !== -1) {
                    reorderResource(activeIndex, overIndex);
                  }
                }
                setActiveResource(null);
              }}
            >
              <SortableContext
                items={resources}
                strategy={verticalListSortingStrategy}
              >
                {resources.map((resource, index) => (
                  <SortableResourceTableRow
                    key={resource.id}
                    index={index}
                    resource={resource}
                    listWidth={listWidth}
                    rowHeight={rowHeight}
                    gripAreaWidth={gripAreaWidth}
                    resourceColumns={resourceColumns}
                  />
                ))}
              </SortableContext>
              <DragOverlay>
                {activeResource !== null && (
                  <ResourceTableRow
                    index={0}
                    resource={activeResource}
                    listWidth={listWidth}
                    rowHeight={rowHeight}
                    gripAreaWidth={gripAreaWidth}
                    resourceColumns={resourceColumns}
                    isDragging={true}
                  />
                )}
              </DragOverlay>
            </DndContext>
          </div>
        </div>
      </div>
    );
  },
);

export default ResourceTable;
