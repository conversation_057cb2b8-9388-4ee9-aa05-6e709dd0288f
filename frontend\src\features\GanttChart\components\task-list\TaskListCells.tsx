// src/features/GanttChart/components/task-list/TaskListCells.tsx
import { useShallow } from "zustand/react/shallow";

import { DragHandleProps, Task } from "@/common/types";
import { CheckBox } from "@/common/components/Inputs";
import { cn } from "@/common/helpers/tailwindMerge";

import { Column } from "@/stores/taskLists";
import { useGanttSchedulerStore } from "@/stores/ganttScheduler";
import { useEditorModalStore } from "@/stores/editorModal";
import {
  useResouceIdNameMap,
  useTemporalResourceIdNameMap,
} from "@/stores/hooks/ganttScheduler";

import { BarSpan } from "@/features/GanttChart/types/barSpan";
import { gatherChildTasks } from "@/features/GanttChart/helpers/gatherChildTasks";


// cell styling
export const TaskListCell =
  "table-cell inline align-middle whitespace-pre overflow-hidden text-ellipsis";

const TaskListNameWrapper = "flex";
const TaskListExpander =
  "text-gray-600 text-xs px-1 pt-0.5 select-none cursor-pointer ";
const TaskListEmptyExpander = "text-xs pl-4 select-none";

/** Props for the row's "cells." */
export interface TaskListCellsProps {
  task: BarSpan;
  depth: number;
  displayColumns: Column[];
  rowHeight: number;
  gripAreaWidth: number;
  disabled?: boolean;
  isDragging?: boolean;
  /** Called to format date → string */
  toLocaleDateString: (
    date: Date,
    dateTimeOptions: Intl.DateTimeFormatOptions
  ) => string;
  onExpanderClick: (task: Task) => void;
  handleFixedChange: (task: Task) => void;
  handleDoubleClickTaskName: (taskId: string) => void;

  /** Drag handles for DnD kit */
  dragHandleProps?: DragHandleProps;

  /** OPTIONAL for batch selection */
  selectedTaskIds?: string[];
  onMultiSelect?: (tasks: Task[]) => void;
  /** The entire list of tasks, if you want to do "select parent => also select children" */
  allTasks: BarSpan[];
}

const TaskListCells: React.FC<TaskListCellsProps> = ({
  task,
  depth,
  displayColumns,
  rowHeight,
  gripAreaWidth,
  disabled,
  isDragging,
  toLocaleDateString,
  onExpanderClick,
  handleFixedChange,
  handleDoubleClickTaskName,
  dragHandleProps: { setActivatorNodeRef, attributes, listeners } = {},
  selectedTaskIds = [],
  onMultiSelect,
  allTasks,
}) => {
  // global state
  const resourceMap = useResouceIdNameMap();
  const temporalResourceMap = useTemporalResourceIdNameMap();
  const timeUnit = useGanttSchedulerStore.use.timeUnit();
  const showEditorModal = useEditorModalStore.use.showEditorModal();
  const isDraggable = useGanttSchedulerStore(
    useShallow((state) => state.sortState.type === "neutral" && !disabled),
  );

  // Choose dateTimeOptions based on timeUnit
  let dateTimeOptions: Intl.DateTimeFormatOptions;
  switch (timeUnit) {
    case "daybyday":
    case "hours":
      dateTimeOptions = {
        weekday: "short",
        year: "numeric",
        month: "numeric",
        day: "numeric",
        hour12: false,
        hour: "numeric",
        minute: "numeric",
      };
      break;
    case "days":
    default:
      dateTimeOptions = {
        weekday: "short",
        year: "numeric",
        month: "numeric",
        day: "numeric",
      };
      break;
  }

  // For projects with children - determine if this task has children
  // allTasks now contains the complete task list, so we can always check for children
  const hasChildren = task.type === 'project' && allTasks.some(t => t.project === task.id);
  let expanderSymbol = "";

  // Only show expander for projects that actually have children
  if (hasChildren) {
    if (task.hideChildren === true) {
      expanderSymbol = "▶"; // collapsed - children are hidden
    } else {
      // Default state (false or undefined) should be expanded - children are visible
      expanderSymbol = "▼"; // expanded
    }
  }

  return (
    <>
      {/* 2) The other columns */}
      {displayColumns.map((col) => {
        switch (col.name) {
          case "multiSelected":
            const isChecked = selectedTaskIds.includes(task.id);
            return (
              <div
                key={`${task.id}--${col.name}`}
                className={cn(
                  TaskListCell,
                  "flex items-center justify-center",
                  // col.name==="multiSelected" && "bg-yellow-500"
                )}
                style={{
                  minWidth: col.width,
                  maxWidth: col.width,
                  minHeight: rowHeight,
                  maxHeight: rowHeight,
                }}
              >
                <CheckBox
                  checked={isChecked}
                  onChange={(e) => {
                    if (!onMultiSelect) return;
                    let newIDs = [...selectedTaskIds];
                    if (e.target.checked) {
                      // user just checked => add this task
                      if (!newIDs.includes(task.id)) {
                        newIDs.push(task.id);
                      }

                      // Also gather children
                      const children = gatherChildTasks(task, allTasks);
                      for (const c of children) {
                        if (!newIDs.includes(c.id)) {
                          newIDs.push(c.id);
                        }
                      }
                    } else {
                      // user just unchecked => remove this task + any of its children
                      const children = gatherChildTasks(task, allTasks).map(x=>x.id);
                      newIDs = newIDs.filter(id => id !== task.id && !children.includes(id));
                    }
                    // Now call onMultiSelect with the actual tasks
                    const selected = allTasks.filter(x => newIDs.includes(x.id));
                    onMultiSelect(selected);
                  }}
                />
              </div>
            );
            case "name":
              return (
                <div
                  key={`${task.id}--${col.name}`}
                  className={TaskListCell}
                  style={{
                    minWidth: col.width,
                    maxWidth: col.width,
                    minHeight: rowHeight,
                    maxHeight: rowHeight,
                  }}
                  title={task.name}
                  onDoubleClick={() => handleDoubleClickTaskName(task.id)}
                >
                  <div className={TaskListNameWrapper}>
                    {[...Array(depth)].map((_, i) => (
                      <span className="pl-4" key={`indent-${i}`} />
                    ))}

                    {/* Expander (if any) */}
                    {expanderSymbol ? (
                      <div
                        className={TaskListExpander}
                        onClick={(e) => {
                          e.stopPropagation();
                          onExpanderClick(task);
                        }}
                      >
                        {expanderSymbol}
                      </div>
                    ) : (
                      <div className={TaskListEmptyExpander}>
                        {/* Empty space for alignment when no expander */}
                      </div>
                    )}

                    {/* Task Name with Dragging Handling */}
                    <div
                      style={{ maxHeight: rowHeight }}
                      className={cn(
                        isDragging ? "cursor-grabbing" : "cursor-grab",
                        !isDraggable && "cursor-default"
                      )}
                      ref={setActivatorNodeRef}
                      {...attributes}
                      {...listeners}
                      onMouseDown={(e) => {
                        const el = e.target as HTMLElement;
                        // Prevent drag when clicking on expander or other interactive elements
                        if (el.closest(".text-xs") || el.closest(".cursor-pointer")) {
                          e.stopPropagation();
                        }
                      }}
                    >
                      {task.name}
                    </div>
                  </div>
                </div>
              );

          case "start":
          case "end":
          case "deadline":
          case "startNoEarlierThan": {
            const cellValue = task[col.name] as Date | null;
            return (
              <div
                key={`${task.id}--${col.name}`}
                className={TaskListCell}
                style={{
                  minWidth: col.width,
                  maxWidth: col.width,
                  minHeight: rowHeight,
                  maxHeight: rowHeight,
                }}
                onDoubleClick={() => {
                  showEditorModal({
                    span: task,
                    focusTarget: col.name,
                  });
                }}
              >
                &nbsp;
                {cellValue !== null &&
                  toLocaleDateString(cellValue, dateTimeOptions)}
              </div>
            );
          }

          case "progress":
            return (
              <div
                key={`${task.id}--${col.name}`}
                className={TaskListCell}
                style={{
                  minWidth: col.width,
                  maxWidth: col.width,
                  minHeight: rowHeight,
                  maxHeight: rowHeight,
                }}
              >
                <div
                  className="mx-4 rounded-full bg-gray-200"
                  style={{ opacity: task.type === "milestone" ? 0 : 1 }}
                >
                  {task.progress ? (
                    <div
                      className="rounded-full bg-blue-600 p-0.5 text-center text-xs font-medium leading-none text-blue-100"
                      style={{
                        width: `${task.progress.toFixed(0)}%`,
                      }}
                    >
                      {`${task.progress.toFixed(0)}%`}
                    </div>
                  ) : (
                    <div className="p-0.5 text-center text-xs font-medium leading-none text-gray-600">
                      {`${task.progress}%`}
                    </div>
                  )}
                </div>
              </div>
            );

          case "resource":
            return (
              <div
                key={`${task.id}--${col.name}`}
                className={TaskListCell}
                style={{
                  minWidth: col.width,
                  maxWidth: col.width,
                  minHeight: rowHeight,
                  maxHeight: rowHeight,
                }}
              >
                <div className="HorizontalScroll">
                  <div className="mx-4 flex items-center gap-x-2">
                    {task.resource.map((r) => {
                      const name = resourceMap.get(r.resourceId) || "???";
                      return (
                        <div
                          key={`${task.id}-resource-${r.resourceId}`}
                          className="whitespace-nowrap rounded-xs border border-gray-300 bg-gray-200 px-2"
                        >
                          {name}:{r.consumption}
                        </div>
                      );
                    })}
                  </div>
                </div>
              </div>
            );

          case "temporalResource":
            return (
              <div
                key={`${task.id}--${col.name}`}
                className={TaskListCell}
                style={{
                  minWidth: col.width,
                  maxWidth: col.width,
                  minHeight: rowHeight,
                  maxHeight: rowHeight,
                }}
              >
                <div className="HorizontalScroll">
                  <div className="mx-4 flex items-center gap-x-2">
                    {task.temporalResource.map((r) => {
                      const name = temporalResourceMap.get(
                        r.temporalResourceId
                      );
                      return (
                        <div
                          key={`${task.id}-tempres-${r.temporalResourceId}`}
                          className="whitespace-nowrap rounded-xs border border-gray-300 bg-gray-200 px-2"
                        >
                          {name}:{r.consumption}
                        </div>
                      );
                    })}
                  </div>
                </div>
              </div>
            );

          case "fixed":
            return (
              <div
                key={`${task.id}--${col.name}`}
                className={TaskListCell}
                style={{
                  minWidth: col.width,
                  maxWidth: col.width,
                  minHeight: rowHeight,
                  maxHeight: rowHeight,
                }}
                onClick={(e) => e.stopPropagation()}
              >
                &nbsp;
                <CheckBox
                  className="ml-4"
                  checked={task.fixed}
                  onChange={(e) => {
                    e.stopPropagation();
                    console.log(`[Fixed Checkbox] Task ${task.name}: ${task.fixed} -> ${e.target.checked}`);
                    // Use the actual checkbox state from the event
                    handleFixedChange({
                      ...task,
                      fixed: e.target.checked,
                    });
                  }}
                  disabled={disabled}
                />
              </div>
            );

          default:
            // fallback
            return (
              <div
                key={`${task.id}--${col.name}`}
                className={TaskListCell}
                style={{
                  minWidth: col.width,
                  maxWidth: col.width,
                  minHeight: rowHeight,
                  maxHeight: rowHeight,
                }}
              >
                &nbsp;
              </div>
            );
        }
      })}
    </>
  );
};

export default TaskListCells;
