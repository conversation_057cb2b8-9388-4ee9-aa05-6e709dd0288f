import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { css } from "@emotion/react";

import { TemporalResource } from "@/common/types";

import { TemporalResourceColumn } from "../../hooks/useTemporalResourceListColumns";
import TemporalResourceTableRow from "./TemporalResourceTableRow";

/**
 * {@link SortableTemporalResourceTableRow}のprops
 */
export interface SortableTemporalResourceTableRowProps {
  /** 何行目か */
  index: number;
  /** 表示する期間付きリソース */
  resource: TemporalResource;
  /** 表の表示幅 */
  listWidth: number;
  /** 行の高さ */
  rowHeight: number;
  /** Tableの並び替えを行うエリアの幅 */
  gripAreaWidth: number;
  /** 表に表示する項目の情報 */
  listColumns: TemporalResourceColumn[];
}

/**
 * TemporalResourceTableの一行をdnd-kitでソート可能としたもの
 */
const SortableTemporalResourceTableRow: React.FC<
  SortableTemporalResourceTableRowProps
> = ({ index, resource, listWidth, rowHeight, gripAreaWidth, listColumns }) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    setActivatorNodeRef,
    isDragging,
  } = useSortable({
    id: resource.id,
  });

  return (
    <div
      style={{
        transform: CSS.Translate.toString(transform),
        transition,
      }}
      ref={setNodeRef}
    >
      {isDragging ? (
        <div
          className="table-row text-ellipsis"
          css={css`
            height: ${rowHeight}px;
            max-width: ${listWidth}px;
            min-width: ${listWidth}px;
          `}
        ></div>
      ) : (
        <TemporalResourceTableRow
          index={index}
          resource={resource}
          listWidth={listWidth}
          rowHeight={rowHeight}
          gripAreaWidth={gripAreaWidth}
          listColumns={listColumns}
          isDragging={isDragging}
          dragHandleProps={{ setActivatorNodeRef, attributes, listeners }}
        />
      )}
    </div>
  );
};

export default SortableTemporalResourceTableRow;
