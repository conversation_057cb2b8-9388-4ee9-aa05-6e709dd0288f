import { useMemo } from "react";
import { Virtualizer } from "@tanstack/react-virtual";
import { useShallow } from "zustand/react/shallow";

import { useStylingOptionStore } from "@/stores/stylingOption";
import {
  useNonDeletedHoliday,
  useNonDeletedTask,
} from "@/stores/hooks/ganttScheduler";
import { useGanttSchedulerStore } from "@/stores/ganttScheduler";

import {
  DateHelperScales,
  addToDate,
  isHoliday,
} from "@/common/helpers/date-helper";
import { isUndefined } from "@/common/helpers/typeGuard";
import { Holiday, Resource, Task } from "@/common/types";
import RateCircle from "./RateCircle";
import { taskXCoordinate, taskYCoordinate } from "../../helpers/barHelper";
import RateBar from "./RateBar";

/**
 * {@link ResourceGridContent}のprops
 */
export interface ResourceGridContentProps {
  /** カレンダーの日付リスト */
  dates: Date[];
  /** 表示するリソースのリスト */
  resources: Resource[];
  /** 表の横幅 */
  columnWidth: number;
  /** 表の縦幅 */
  rowHeight: number;
  /** 表のパディングサイズ */
  paddingSize?: number;
  /** 列方向の仮想スクロールハンドラ */
  columnVirtualizer?: Virtualizer<HTMLDivElement, Element>;
}

/**
 * リソースの使用状況のグラフ
 */
const ResourceGridContent: React.FC<ResourceGridContentProps> = ({
  dates = [],
  resources = [],
  columnWidth,
  rowHeight,
  paddingSize = 7,
  columnVirtualizer,
}) => {
  const tasks = useNonDeletedTask() || [];
  const holiday = useNonDeletedHoliday() || {};
  const graphStyle = useStylingOptionStore(
    (state) => state.resourceMonitor.graphStyle,
  );
  const scale: DateHelperScales = useGanttSchedulerStore(
    useShallow((state) => {
      const timeUnit = state.timeUnit;
      if (timeUnit === "hours") return "hour";
      else return "day";
    }),
  );

  const virtualItems = columnVirtualizer?.getVirtualItems?.() || [];

  const resourceGraph: React.ReactNode[] = useMemo(() => {
    return (resources || []).map((r, index) => {
      const resourceGraph: React.ReactNode[] = [];
      const targetDates = virtualItems.length > 0
        ? virtualItems.map(({ index }) => dates?.[index]).filter(Boolean)
        : (dates || []);
      (targetDates || []).forEach((_, i) => {
        if (i === targetDates.length - 1) return;
        const start = targetDates[i];
        const end = targetDates[i + 1];
        if (!start || !end) return;
        // [start, end)の期間が全て休日なら飛ばす。
        if (isDurationHoliday(start, end, holiday)) return;
        // [start, end)のリソース消費の最大量を求める
        const maxConsumption = getMaxResourceConsumption(
          tasks || [],
          start,
          end,
          r.id,
          scale,
        );
        // styleがcircleで消費量が0ならば、飛ばす。
        if (graphStyle === "circle" && maxConsumption === 0) return;
        // 表示位置をstartで計算して、svgをリストに入れる。
        const x = taskXCoordinate(start, dates, columnWidth);
        const y = taskYCoordinate(
          index,
          rowHeight,
          rowHeight - paddingSize * 2,
        );
        resourceGraph.push(
          <svg
            key={`${r.id} ${start.toISOString()}`}
            xmlns="http://www.w3.org/2000/svg"
            width={columnWidth}
            height={rowHeight - paddingSize * 2}
            x={x}
            y={y}
          >
            {graphStyle === "bar" ? (
              <RateBar numerator={maxConsumption} denominator={r.quantity} />
            ) : (
              <RateCircle numerator={maxConsumption} denominator={r.quantity} />
            )}
          </svg>,
        );
      });
      return resourceGraph;
    });
  }, [
    resources,
    virtualItems,
    dates,
    holiday,
    tasks,
    scale,
    graphStyle,
    columnWidth,
    rowHeight,
    paddingSize,
  ]);

  return <g>{resourceGraph}</g>;
};

/**
 * 指定したリソースIDのリソースが同時に使われている数をstart ~ endまでの期間で時間単位ごとに計算し、
 * その期間中で最大の数を返す関数。
 *
 * @param {Task[]} tasks - 全てのタスク
 * @param {Date} start - 開始
 * @param {Date} end - 終了
 * @param {string} resourceId - 集計するリソースのID
 * @param {DateHelperScales} scale - 集計する時間単位
 * @returns {number} 同時に使われているリソースの数の最大
 */
const getMaxResourceConsumption = (
  tasks: Task[],
  start: Date,
  end: Date,
  resourceId: string,
  scale: DateHelperScales,
) => {
  // start ~ endの外側にあるtaskは除いておく。
  const targetTasks = tasks.filter(
    (t) =>
      t.resource.some((r) => r.resourceId === resourceId) &&
      t.start !== null &&
      t.end !== null &&
      !(t.end.getTime() < start.getTime() || t.start.getTime() > end.getTime()),
  );
  if (targetTasks.length === 0) return 0;

  // [start, end)の範囲で、リソース使用量の最大値を探索。
  let maxConsuption = 0;
  let targetDate = start;
  while (targetDate.getTime() < end.getTime()) {
    const targets = targetTasks.filter(
      (t) =>
        t.start !== null &&
        t.end !== null &&
        t.start.getTime() <= targetDate.getTime() &&
        t.end.getTime() > targetDate.getTime(),
    );
    const newConsuption = targets.reduce((prev, current) => {
      const targetResource = current.resource.find(
        (r) => r.resourceId === resourceId,
      );
      if (!isUndefined(targetResource))
        return prev + targetResource.consumption;
      else return prev;
    }, 0);
    if (maxConsuption < newConsuption) maxConsuption = newConsuption;

    targetDate = addToDate(targetDate, 1, scale);
  }
  return maxConsuption;
};

/**
 * [start, end)の範囲で1時間刻みでみたときに、全ての時間が休日かどうかを判定する関数。
 *
 * @param {Date} start 開始
 * @param {Date} end 終了
 * @param {Holiday} holiday 休日
 * @returns {boolean} 休日ならば、true. 休日でないならばfalse
 */
const isDurationHoliday = (start: Date, end: Date, holiday: Holiday) => {
  const targetDates: Date[] = [];
  let target = start;
  while (target.getTime() < end.getTime()) {
    targetDates.push(target);
    target = addToDate(target, 1, "hour");
  }
  return targetDates.every((date) => isHoliday(date, holiday));
};

export default ResourceGridContent;
