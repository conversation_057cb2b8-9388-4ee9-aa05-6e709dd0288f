import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { ActionMeta, MultiValue } from "react-select";
import { Cog8ToothIcon } from "@heroicons/react/24/outline";

import { graphStyles } from "@/stores/stylingOption";
import { useStylingOptionStore } from "@/stores/stylingOption";
import { useResourceMonitorStore } from "@/stores/resourceMonitor";
import { useNonDeletedResource } from "@/stores/hooks/ganttScheduler";

import { Toggle } from "@/common/components/Inputs/Toggle";
import { Label } from "@/common/components/Other";
import { BaseSelect } from "@/common/components/Selects";
import { logger } from "@/common/helpers/logger";
import { isUndefined } from "@/common/helpers/typeGuard";

type onChangeMultiSelect = (
  newValue: MultiValue<{
    value: string;
    label: string;
  }>,
  actionMeta: ActionMeta<{
    value: string;
    label: string;
  }>,
) => void;

/**
 * リソースモニターの設定画面
 */
const ResourceMonitorSetting: React.FC = () => {
  // global state
  const { displayIds, isDisplayed } = useResourceMonitorStore.use.resource();
  const resource = useNonDeletedResource();
  const resourceOptions = resource.map(({ id, name }) => ({
    value: id,
    label: name,
  }));
  const graphStyle = useStylingOptionStore(
    (state) => state.resourceMonitor.graphStyle,
  );
  const updateGraphStyle = useStylingOptionStore.use.updateGraphStyle();
  const changeResourceIsDisplayed =
    useResourceMonitorStore.use.changeResourceIsDisplayed();
  const replaceResourceDisplayIds =
    useResourceMonitorStore.use.replaceResourceDisplayIds();

  const handleChangeDisplayIds: onChangeMultiSelect = (_, actionMeta) => {
    const { action, option, removedValue } = actionMeta;
    switch (action) {
      case "select-option":
        if (!isUndefined(option))
          replaceResourceDisplayIds([...displayIds, option.value]);
        break;
      case "remove-value":
        if (!isUndefined(removedValue))
          replaceResourceDisplayIds(
            displayIds.filter((id) => id !== removedValue.value),
          );
        break;
      case "clear":
        replaceResourceDisplayIds([]);
        break;
      case "pop-value":
      case "create-option":
      case "deselect-option":
        logger.warn(`Not Implemented action: ${action}`);
        break;
      default:
        break;
    }
  };

  return (
    <div className="border-t">
      <Accordion type="single" collapsible>
        <AccordionItem value="resource-monitor">
          <AccordionTrigger>
            <span className="flex items-center gap-2">
              <Cog8ToothIcon className="h-6 w-6 stroke-2" />
              リソースモニター
            </span>
          </AccordionTrigger>
          <AccordionContent>
            <div className="max-w-(--breakpoint-lg) space-y-3">
              <div>
                <Label>表示</Label>
                <Toggle
                  checked={isDisplayed}
                  onChange={() => {
                    changeResourceIsDisplayed();
                  }}
                />
              </div>
              <div>
                <Label>表示するリソース</Label>
                <BaseSelect
                  isMulti
                  closeMenuOnSelect={false}
                  options={resourceOptions}
                  value={resourceOptions.filter(({ value }) =>
                    displayIds.includes(value),
                  )}
                  onChange={handleChangeDisplayIds}
                />
              </div>
              <div>
                <Label>リソースモニターのグラフスタイル</Label>
                <BaseSelect
                  options={graphStyleOptions}
                  value={graphStyleOptions.find(
                    ({ value }) => value === graphStyle,
                  )}
                  onChange={(newValue) => {
                    if (newValue !== null) updateGraphStyle(newValue.value);
                    return;
                  }}
                />
              </div>
            </div>
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </div>
  );
};

const graphStyleOptions = graphStyles.map((value) => {
  return { value, label: value };
});

export default ResourceMonitorSetting;
