import { css } from "@emotion/react";

import { SVGGripDotsOutline } from "@/common/components/Svgs";
import { DragHandleProps, Resource } from "@/common/types";
import { cn } from "@/common/helpers/tailwindMerge";

import { ResourceColumn } from "../../hooks/useResourceListColumns";

/**
 * {@link ResourceTableRow}のprops
 */
export interface ResourceTableRowProps {
  /** 何行目か */
  index: number;
  /** 表示するリソース */
  resource: Resource;
  /** Tableの表示幅。（タスクリストと同期させる） */
  listWidth: number;
  /** 行の高さ */
  rowHeight: number;
  /** 表の並び替えを行うエリアの幅 */
  gripAreaWidth: number;
  /** 表示する項目情報 */
  resourceColumns: ResourceColumn[];
  /** ドラッグ中か否か */
  isDragging?: boolean;
  /** dndのdrag handleに渡すprops */
  dragHandleProps?: DragHandleProps;
}

/**
 * ResourceTableの一行
 */
const ResourceTableRow: React.FC<ResourceTableRowProps> = ({
  index,
  resource,
  listWidth,
  rowHeight,
  gripAreaWidth,
  resourceColumns,
  isDragging,
  dragHandleProps: { setActivatorNodeRef, attributes, listeners } = {},
}) => {
  return (
    <div
      className={cn(
        "overflow-hidden bg-white",
        (index + 1) % 2 === 0 && "bg-gray-100",
      )}
      style={{
        maxWidth: listWidth,
      }}
    >
      <div
        css={css`
          height: ${rowHeight}px;
          max-width: ${listWidth}px;
          min-width: ${listWidth}px;
        `}
        className="table-row text-ellipsis"
      >
        <div
          className={cn(
            "whitespace-no-wrap table-cell cursor-grab overflow-hidden text-ellipsis align-middle",
            isDragging && "cursor-grabbing",
          )}
          css={css`
            min-width: ${gripAreaWidth}px;
            max-width: ${gripAreaWidth}px;
            min-height: ${rowHeight}px;
            max-height: ${rowHeight}px;
          `}
          ref={setActivatorNodeRef}
          {...attributes}
          {...listeners}
        >
          <SVGGripDotsOutline className="h-6 w-6 stroke-gray-800" />
        </div>
        {resourceColumns.map((col) => (
          <div
            key={col.name}
            css={css`
              min-width: ${col.width}px;
              max-width: ${col.width}px;
            `}
            className="whitespace-no-wrap table-cell overflow-hidden text-ellipsis align-middle"
          >
            <div className="pl-4">{resource[col.name]}</div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default ResourceTableRow;
