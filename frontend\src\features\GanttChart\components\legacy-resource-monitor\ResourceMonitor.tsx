// src/features/GanttChart/components/resource-monitor/ResourceMonitor.tsx
import React, { SyntheticEvent, useEffect, useRef, useState } from "react";
import { css } from "@emotion/react";
import { motion, AnimatePresence } from "framer-motion";
import { useVirtualizer } from "@tanstack/react-virtual";
import { Cog8ToothIcon } from "@heroicons/react/24/outline";

import { Calendar, CalendarProps } from "../calendar/Calendar";
import HorizontalResizer, {
  HorizontalResizerProps,
} from "../other/HorizontalResizer";
import { ResourceColumn } from "../../hooks/useResourceListColumns";
import { useScroll } from "../../hooks/useScroll";

import ResourceTable from "./ResourceTable";
import ResourceGrid from "./ResourceGrid";
import { HorizontalScroll } from "../other/HorizontalScroll";
import ResourceMonitorSetting from "./ResourceMonitorSetting";
import VerticalResizer from "../other/VerticalResizer";
import { useDisplayResource } from "../../hooks/useDisplayResource";
import { useStylingOptionStore } from "@/stores/stylingOption";

// (1) import our monitorHeights store
import { useMonitorHeights } from "@/stores/monitorHeights";
import { Dialog, DialogContent, DialogTrigger } from "@/components/ui/dialog";

/** {@link ResourceMonitor} のprops */
export interface ResourceMonitorProps {
  resourceColumns: ResourceColumn[];
  changeColumnWidth: (name: string, width: number) => void;
  listWidth: number;
  svgWidth: number;
  headerHeight: number;
  rowHeight: number;
  columnWidth: number;
  gripAreaWidth: number;
  scrollX: number;
  todayColor: string;
  calendarProps: Omit<CalendarProps, "columnVirtualizer">;
  horizontalResizerProps: HorizontalResizerProps;
  resourceGridRef: React.RefObject<HTMLDivElement>;
  resourceTableWidth: number;
  handleScrollX: (event: SyntheticEvent<HTMLDivElement>) => void;
}

export const ResourceMonitor: React.FC<ResourceMonitorProps> = ({
  resourceColumns,
  changeColumnWidth,
  listWidth,
  svgWidth,
  headerHeight,
  rowHeight,
  columnWidth,
  gripAreaWidth,
  todayColor,
  calendarProps,
  scrollX,
  horizontalResizerProps,
  resourceGridRef,
  resourceTableWidth,
  handleScrollX,
}) => {
  // const {
  //   // resourceColumns,
  //   // changeColumnWidth,
  //   // listWidth,
  //   // svgWidth,
  //   // headerHeight,
  //   // rowHeight,
  //   // columnWidth,
  //   // gripAreaWidth,
  //   // todayColor,
  //   // calendarProps,
  //   // horizontalResizerProps,
  //   // resourceGridRef,
  //   // resourceTableWidth,
  //   // handleScrollX,
  // } = props;

  // toggles
  const showResourceMonitor = useStylingOptionStore(
    (state) => state.showResourceMonitor
  );
  const showTemporalResourceMonitor = useStylingOptionStore(
    (state) => state.showTemporalResourceMonitor
  );

  // from store
  const { resource } = useDisplayResource();
  const resourceMonitorFullHeight = rowHeight * resource.length + headerHeight;

  // (4) Resource monitor height from zustand
  const resourceHeight = useMonitorHeights((s) => s.resourceHeight);
  const setResourceHeight = useMonitorHeights((s) => s.setResourceHeight);

  // For scrolling
  const resourceMonitorRef = useRef<HTMLDivElement>(null);
  const resourceTableRef = useRef<HTMLDivElement>(null);

  const { scroll: scrollY, handleScroll: handleScrollY } = useScroll({
    ref: resourceMonitorRef as any,
    type: "vertical",
    max: resourceMonitorFullHeight,
  });

  const {
    scroll: resourceTableScrollX,
    handleScroll: handleResourceTableScrollX,
  } = useScroll({
    ref: resourceTableRef as any,
    type: "horizontal",
    max: resourceTableWidth,
  });

  const columnVirtualizer = useVirtualizer({
    count: calendarProps.dateSetup.dates.length,
    getScrollElement: () => resourceGridRef.current,
    estimateSize: () => columnWidth,
    horizontal: true,
  });

  useEffect(() => {
    if (resourceGridRef.current) {
      resourceGridRef.current.scrollLeft = scrollX;
    }
  }, [scrollX]);

  useEffect(() => {
    if (resourceMonitorRef.current) {
      console.log('[ResourceMonitor] Height:', resourceMonitorRef.current.offsetHeight);
      console.log('[ResourceMonitor] Client Height:', resourceMonitorRef.current.clientHeight);
    }
  }, []);

  // Settings dialog state
  const [openSettings, setOpenSettings] = useState(false);

  // If toggle is off, unmount
  if (!showResourceMonitor) return null;

  // (5) Our reversed vertical resizing:
  // Suppose dragging "up" should reduce the height

  const handleVerticalResize = (newSize: number) => {
    const min = 50;
    const max = Math.max(resourceMonitorFullHeight + 50, 800);
    const clamped = Math.max(min, Math.min(newSize, max));
    setResourceHeight(clamped);
  };

  // Slide animation variants
  const slideVariants = {
    hidden: { y: "100%", opacity: 0 },
    visible: { y: 0, opacity: 1 },
    exit: { y: "100%", opacity: 0 },
  };

  return (
    <AnimatePresence>
      {showResourceMonitor && (
        <motion.div
          key="resource-monitor"
          initial="hidden"
          animate="visible"
          exit="exit"
          variants={slideVariants}
          transition={{ type: "spring", stiffness: 300, damping: 30 }}
          className="relative flex flex-col h-full"
          ref={resourceMonitorRef}
          style={{ height: "100%" }}
        >
          <div className="flex items-center justify-between border-b border-gray-200 bg-background dark:border-gray-700" style={{ height: headerHeight }}>
            <div
              style={{ width: listWidth }}
              className="flex-shrink-0 flex items-center justify-center"
            >
              <span className="text-xs font-medium text-gray-500 dark:text-gray-400">
                Resources
              </span>
            </div>
            {/* Settings button opens dialog */}
            <Dialog open={openSettings} onOpenChange={setOpenSettings}>
              <DialogTrigger asChild>
                <button
                  className="ml-2 mr-4 p-1 rounded hover:bg-gray-100 dark:hover:bg-gray-800"
                  aria-label="リソースモニター設定"
                  type="button"
                >
                  <Cog8ToothIcon className="w-5 h-5 text-gray-500" />
                </button>
              </DialogTrigger>
              <DialogContent className="max-w-md">
            <ResourceMonitorSetting />
              </DialogContent>
            </Dialog>
          </div>
          <div
            ref={resourceTableRef}
            className="w-full h-full overflow-y-auto overflow-x-hidden"
          >
            <div
              className="relative flex overflow-hidden p-0 outline-0"
              css={css`
                width: 100%;
              `}
            >
              <div
                css={css`
                  min-width: ${listWidth}px;
                  max-width: ${listWidth}px;
                `}
              >
                <ResourceTable
                  resources={resource}
                  listWidth={listWidth}
                  rowHeight={rowHeight}
                  headerHeight={headerHeight}
                  contentHeight={resourceHeight - headerHeight - 20}
                  gripAreaWidth={gripAreaWidth}
                  scrollX={resourceTableScrollX}
                  scrollY={scrollY}
                  resourceColumns={resourceColumns}
                  changeColumnWidth={changeColumnWidth}
                />
              </div>
              <HorizontalResizer {...horizontalResizerProps} />
              <div
                className="hidden-scrollbar m-0 overflow-x-scroll p-0"
                ref={resourceGridRef}
                dir="ltr"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width={svgWidth}
                  height={calendarProps.headerHeight}
                >
                  <Calendar
                    {...calendarProps}
                    columnVirtualizer={columnVirtualizer}
                  />
                </svg>
                <ResourceGrid
                  dates={calendarProps.dateSetup.dates}
                  resources={resource}
                  svgWidth={svgWidth}
                  rowHeight={rowHeight}
                  columnWidth={columnWidth}
                  contentHeight={resourceHeight - headerHeight - 20}
                  todayColor={todayColor}
                  scrollY={scrollY}
                  columnVirtualizer={columnVirtualizer}
                />
              </div>
            </div>
          </div>
          <VerticalResizer
            size={resourceHeight}
            handleChangeSize={handleVerticalResize}
          />
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default ResourceMonitor;
