// src/features/GanttChart/components/GanttContainer.tsx
import { useCallback, useState, useRef, useEffect } from "react";

import { useStylingOptionStore } from "@/stores/stylingOption";
import { useGanttSchedulerStore } from "@/stores/ganttScheduler";
import { useChartModeStore } from "@/stores/chartModeStore";

import { Span, Task, ViewMode } from "@/common/types";
import { makeEmptySpan } from "@/common/helpers/other-helper";
import { logger } from "@/common/helpers/logger";

import { BarSpan } from "../types/barSpan";
import { Gantt } from "./gantt/Gantt";
import SpanEditorModal from "./modal/SpanEditorModal";
import { useDialog } from "@/common/hooks/useDialog";
import { useEditorModalStore } from "@/stores/editorModal";
import { useToast } from "@/hooks/use-toast";
import { Toaster } from "@/components/ui/toaster";
import { useAllCommentaries} from "@/features/GanttChart/api/memoService";

import MultiSelectStickyNote from "@/features/GanttChart/components/other/MultiSelectStickyNote";
import { useParams } from "react-router-dom";
import { useNonDeletedTask } from "@/stores/hooks/ganttScheduler";

// a small helper for date => yyyy-mm-dd
function formatDate(date: Date | null): string {
  return date ? date.toISOString().slice(0, 10) : "";
}

export interface GanttContainerProps {
  readOnly?: boolean;
  height: number;
}

const GanttContainer: React.FC<GanttContainerProps> = ({ readOnly, height }) => {
  // Global store methods
  const view = useStylingOptionStore.use.viewMode();
  const remove = useGanttSchedulerStore.use.remove();
  const bulkRemove = useGanttSchedulerStore.use.bulkRemove();
  const updateCriticalPathWithinProject =
    useGanttSchedulerStore.use.updateCriticalPathWithinProject();
  const update = useGanttSchedulerStore.use.update();
  const updateWithDependencies =
    useGanttSchedulerStore.use.updateWithDependencies();
  const bulkCopy = useGanttSchedulerStore.use.bulkCopy();
  const bulkMove = useGanttSchedulerStore.use.bulkMove();

  // Get schedule ID for commentaries
  const { scheduleId } = useParams<{ scheduleId: string }>();

  // Local states
  const [taskIsChanged, setTaskIsChanged] = useState(false);
  const [clickedTask, setClickedTask] = useState<BarSpan | null>(null);

  // Load all commentaries for this schedule
  const { data: commentaries = [], isLoading: loadingCommentaries } = useAllCommentaries(scheduleId);

  // multi-select
  const [selectedTaskIds, setSelectedTaskIds] = useState<string[]>([]);
  const [multiSelectDialogOpen, setMultiSelectDialogOpen] = useState(false);

  // Hooks
  const stylingOption = useStylingOptionStore.use.gantt();
  const calendarStylingOption = useStylingOptionStore.use.calendar();
  const { toast } = useToast();
  const {showDialog, renderDialog} = useDialog();
  // EditorModal
  const editorModalProps = useEditorModalStore.use.props();
  const showEditorModal = useEditorModalStore.use.showEditorModal();
  const closeEditorModal = useEditorModalStore.use.closeEditorModal();

  // Temporarily bypass useNonDeletedTask to debug
  const rawTasks = useGanttSchedulerStore.use.tasks();
  const tasks = rawTasks.filter(t => t.crudType !== "delete");

  // Debug logging (commented out for performance)
  // console.log('[GanttContainer] Tasks after filtering:', tasks.length);
  // console.log('[GanttContainer] Raw tasks from store:', rawTasks.length);

  // Column width logic
  let columnWidth: number;
  switch (view) {
    case ViewMode.Day:
      columnWidth = calendarStylingOption.columnWidth.day;
      break;
    case ViewMode.QuarterDay:
      columnWidth = calendarStylingOption.columnWidth.quarterDay;
      break;
    case ViewMode.HalfDay:
      columnWidth = calendarStylingOption.columnWidth.halfDay;
      break;
    case ViewMode.Hour:
      columnWidth = calendarStylingOption.columnWidth.hour;
      break;
    case ViewMode.Week:
      columnWidth = calendarStylingOption.columnWidth.week;
      break;
    case ViewMode.Month:
      columnWidth = calendarStylingOption.columnWidth.month;
      break;
    case ViewMode.Year:
      columnWidth = calendarStylingOption.columnWidth.year;
      break;
    default:
      columnWidth = 65;
      break;
  }

  // Add ref for the container
  const containerRef = useRef<HTMLDivElement>(null);

  // Example fix-change
  const handleFixedChange = (task: Task) => {
    logger.log(`On confirm change: Id=${task.id} Name=${task.name}`);
    // Get the current task from store to ensure we have the latest state
    const currentTask = tasks.find(t => t.id === task.id);
    if (currentTask) {
      // Only modify fixed property, preserve all other properties from store
      const updatedTask = {
        ...currentTask,
        fixed: task.fixed,
      };
      update(updatedTask);
      if (task.fixed) {
        toast({ title: `${task.name}を固定しました。` });
      } else {
        toast({ title: `${task.name}の固定を解除しました。` });
      }
    }
  };

  // Example: handle date change
  const handleTaskDateChange = useCallback(
    (
      task: Task,
      _children: Task[],
      message: { title?: string; description?: string } = {}
    ) => {
      logger.log(`On date change: Id=${task.id} Name=${task.name}`);
      updateWithDependencies(task);
      if (message.title === undefined && message.description === undefined) {
        toast({
          title: `${task.name}の日付を変更しました。${formatDate(task.start)}から${formatDate(task.end)}`,
        });
      } else {
        toast({
          title: message.title,
          description: message.description,
        });
      }
    },
    [updateWithDependencies, toast],
  );

  // Example: handle start/end change
  const handleTaskStartEndChange = useCallback(
    async (task: Task) => {
      logger.log(`On start/end change: Id=${task.id} Name=${task.name}`);
      updateWithDependencies(task);
      toast({
        title: `${task.name}の期間を
        ${formatDate(task.start)}から${formatDate(task.end)}までに
        変更しました。
        `,
      });
      if (task.project) {
        await updateCriticalPathWithinProject(task.project, {
          history: { replace: true },
        });
        toast({
          title: `${task.name}のクリティカルパスを再計算しました。`,
        });
      }
    },
    [updateWithDependencies, updateCriticalPathWithinProject, toast],
  );

  // Example: handle delete
  const handleTaskDelete = (task: Task) => {
    showDialog({
      type: "alert",
      title: `${task.name}を削除しますか？`,
      onConfirm: () => {
        remove(task);
        toast({ title: `${task.name}を削除しました。` });
      },
      confirmText: "削除",
      cancelText: "キャンセル",
    });
  };

  // Example: handle progress
  const handleProgressChange = async (task: Task) => {
    logger.log(`On progress change: Id=${task.id} Name=${task.name}`);
    update(task);
    toast({ title: `${task.name}の進捗を${task.progress}%にしました。` });
  };

  // Dbl click => open editor
  const handleDblClick = (span: Span) => {
    showEditorModal({ span });
    logger.log(`On Double Click: Id=${span.id} Name=${span.name}`);
  };

  // Single click => setClickedTask
  const handleClick = (task: BarSpan) => {
    if (!taskIsChanged) {
      logger.log(`On Click: Id=${task.id} Name=${task.name}`);
      setClickedTask((prev) => (prev && prev.id === task.id ? null : task));
    }
    setTaskIsChanged(false);
  };

  // Get the current chart mode
  const chartMode = useChartModeStore((s) => s.getScheduleChartMode(scheduleId || ""));
  const isApprovalMode = chartMode === "approval";

  // Multi-select => show sticky note
  const handleMultiSelect = (taskArray: Task[]) => {
    // Return early if in approval mode
    if (isApprovalMode) {
      return;
    }

    const selectedTaskIdsArray = taskArray.map((t) => t.id);
    // to set then to array to remove duplicates
    setSelectedTaskIds([...new Set(selectedTaskIdsArray)]);
    if (taskArray.length > 0) {
      setMultiSelectDialogOpen(true);
    }
  };

  // Bulk delete
  function handleBulkDelete(taskIds: string[]) {
    showDialog({
      type: "alert",
      title: `${taskIds.length} tasks will be deleted. Are you sure?`,
      onConfirm: () => {
        bulkRemove(taskIds as any[]);
        toast({ title: `${taskIds.length} tasks deleted!` });
      },
    });
    //clean up selectedTaskIds
    setSelectedTaskIds([]);
    setMultiSelectDialogOpen(false);
  }

  // Bulk copy
  function handleBulkCopy(taskIds: string[]) {
    toast({ title: `Copied ${taskIds.length} tasks!` });
    setMultiSelectDialogOpen(false);
    //clean up selectedTaskIds
    setSelectedTaskIds([]);
    return bulkCopy(taskIds);
  }

  // Bulk move => now accepts a date
  async function handleBulkMove(taskIds: string[], moveDate: Date, message: { title?: string; description?: string } = {}) {
    logger.log("[handleBulkMove] => taskIds=", taskIds, " moveDate=", moveDate.toISOString());
    if (taskIds.length === 0) {
      logger.log("[handleBulkMove] => no taskIds => return");
      return;
    }

    // call store method
    const moveResults = await bulkMove(taskIds, moveDate);
    logger.log("[handleBulkMove] => store returned moveResults =>", moveResults);

    // show a toast
    if (message.title === undefined && message.description === undefined) {
      toast({
        title: `Moved ${taskIds.length} tasks to start at ${formatDate(moveDate)}!`,
      });
    } else {
      toast({
        title: message.title,
        description: message.description,
      });
    }
    //clean up selectedTaskIds
    setSelectedTaskIds([]);
    setMultiSelectDialogOpen(false);
  }

  // Expander
  const handleExpanderClick = (task: Task) => {
    logger.log(
      `On expander click: Id=${task.id} Name=${task.name} hideChildren=${task.hideChildren}`,
    );
    // Get the current task from store to ensure we have the latest state
    const currentTask = tasks.find(t => t.id === task.id);
    if (currentTask) {
      // Only modify hideChildren property, preserve all other properties from store
      update({
        ...currentTask,
        hideChildren: task.hideChildren,
      });
    }
  };

  // Editor open
  const openSpanEditor = (span: Span = makeEmptySpan()) => {
    showEditorModal({ span });
  };

  // Render
  return (
    <div
      className="flex flex-col h-full flex-1"
      ref={containerRef}
      style={{
        height: "100%",
        display: "flex",
        flexDirection: "column",
        flex: 1,
        border: "2px solid red", // Debug border
      }}
    >
      {/* Render the dialog component here */}
      {renderDialog()}

      <SpanEditorModal
        {...editorModalProps}
        onClose={() => {
          closeEditorModal();
        }}
      />
      <Toaster />

      <MultiSelectStickyNote
        open={multiSelectDialogOpen}
        selectedTaskIds={selectedTaskIds}
        onClose={() => setMultiSelectDialogOpen(false)}
        onBulkDelete={handleBulkDelete}
        onBulkCopy={handleBulkCopy}
        onBulkMove={handleBulkMove}
      />

      <div
        className="flex-1 flex flex-col h-full"
        style={{
          flexGrow: 1,
          position: "relative",
          height: "100%",
          flex: 1,
          border: "2px solid blue", // Debug border
        }}
      >
        <Gantt
          tasks={tasks}
          height={height}
          commentaries={commentaries}
          handleFixedChange={handleFixedChange}
          onDateChange={handleTaskDateChange}
          onStartEndChange={handleTaskStartEndChange}
          onDelete={handleTaskDelete}
          onProgressChange={handleProgressChange}
          onDoubleClick={handleDblClick}
          onClick={handleClick}
          onMultiSelect={handleMultiSelect}
          selectedTaskIds={selectedTaskIds}
          abstViewTask={clickedTask}
          spanDetailViewClose={() => {
            setClickedTask(null);
          }}
          openSpanEditor={openSpanEditor}
          setTaskIsChanged={setTaskIsChanged}
          onExpanderClick={handleExpanderClick}
          listCellWidth={155}
          columnWidth={columnWidth}
          readOnly={readOnly}
          {...stylingOption}
        />
      </div>
    </div>
  );
};

export default GanttContainer;
