// src/features/GanttChart/components/temporal-resource-monitor/TemporalResourceMonitor.tsx
import React, { SyntheticEvent, useEffect, useRef } from "react";
import { css } from "@emotion/react";
import { motion, AnimatePresence } from "framer-motion";
import { useVirtualizer } from "@tanstack/react-virtual";

import { Calendar, CalendarProps } from "../calendar/Calendar";
import { VerticalScroll } from "../other/VerticalScroll";
import { HorizontalScroll } from "../other/HorizontalScroll";
import HorizontalResizer, {
  HorizontalResizerProps,
} from "../other/HorizontalResizer";
import { TemporalResourceColumn } from "../../hooks/useTemporalResourceListColumns";
import { useScroll } from "../../hooks/useScroll";
import TemporalResourceTable from "./TemporalResourceTable";
import TemporalResourceGrid from "./TemporalResourceGrid";
import TemporalResourceMonitorSetting from "./TemporalResourceMonitorSetting";
import VerticalResizer from "../other/VerticalResizer";
import { useDisplayTemporalResource } from "../../hooks/useDisplayTemporalResource";
import { useStylingOptionStore } from "@/stores/stylingOption";

// (1) import monitorHeights
import { useMonitorHeights } from "@/stores/monitorHeights";


export interface TemporalResourceMonitorProps {
  listColumns: TemporalResourceColumn[];
  changeColumnWidth: (name: string, width: number) => void;
  listWidth: number;
  svgWidth: number;
  headerHeight: number;
  rowHeight: number;
  columnWidth: number;
  gripAreaWidth: number;
  scrollX: number;
  todayColor: string;
  calendarProps: Omit<CalendarProps, "columnVirtualizer">;
  horizontalResizerProps: HorizontalResizerProps;
  gridRef: React.RefObject<HTMLDivElement>;
  tableWidth: number;
  handleScrollX: (event: SyntheticEvent<HTMLDivElement>) => void;
}

export const TemporalResourceMonitor: React.FC<TemporalResourceMonitorProps> = ({
  listColumns,
  changeColumnWidth,
  listWidth,
  svgWidth,
  headerHeight,
  rowHeight,
  columnWidth,
  gripAreaWidth,
  todayColor,
  calendarProps,
  scrollX,
  horizontalResizerProps,
  gridRef,
  tableWidth,
  handleScrollX,
}) => {
  const temporalResourceMonitorRef = useRef<HTMLDivElement>(null);

  // toggles
  const showTemporalResourceMonitor = useStylingOptionStore(
    (state) => state.showTemporalResourceMonitor
  );
  const showResourceMonitor = useStylingOptionStore(
    (state) => state.showResourceMonitor
  );

  // read the resource panel's height
  const resourceHeight = useMonitorHeights((s) => s.resourceHeight);

  // track our own height
  const temporalHeight = useMonitorHeights((s) => s.temporalHeight);
  const setTemporalHeight = useMonitorHeights((s) => s.setTemporalHeight);

  const { temporalResource, isDisplayed } = useDisplayTemporalResource();
  const monitorFullHeight = rowHeight * temporalResource.length + headerHeight;

  const { scroll: scrollY, handleScroll: handleScrollY } = useScroll({
    ref: temporalResourceMonitorRef,
    type: "vertical",
    max: monitorFullHeight,
  });
  const { scroll: tableScrollX, handleScroll: handleTableScrollX } = useScroll({
    ref: temporalResourceMonitorRef,
    type: "horizontal",
    max: tableWidth,
  });

  const columnVirtualizer = useVirtualizer({
    count: calendarProps.dateSetup.dates.length,
    getScrollElement: () => gridRef.current,
    estimateSize: () => columnWidth,
    horizontal: true,
  });

  useEffect(() => {
    if (gridRef.current) {
      gridRef.current.scrollLeft = scrollX;
    }
  }, [scrollX]);

  // Log dimensions for debugging
  useEffect(() => {
    if (temporalResourceMonitorRef.current) {
      console.log('[TemporalResourceMonitor] Height:', temporalResourceMonitorRef.current.offsetHeight);
      console.log('[TemporalResourceMonitor] Client Height:', temporalResourceMonitorRef.current.clientHeight);
    }
  }, []);

  // Slide animation variants
  const slideVariants = {
    hidden: { y: "100%", opacity: 0 },
    visible: { y: 0, opacity: 1 },
    exit: { y: "100%", opacity: 0 },
  };

  // If toggle is off, unmount
  if (!showTemporalResourceMonitor) return null;

  // If both are on, place Temporal above Resource
  // => bottom = resourceHeight
  // If Resource is off, bottom = 0
  const bottomOffset = showResourceMonitor ? resourceHeight : 0;

  // Reversed vertical resizing
  const handleVerticalResize = (newSize: number) => {
    const min = 50;
    const max = Math.max(monitorFullHeight + 50, 600);
    const clamped = Math.max(min, Math.min(newSize, max));
    setTemporalHeight(clamped);
  };

  return (
    <AnimatePresence>
      {showTemporalResourceMonitor && (
        <motion.div
          key="temporal-resource-monitor"
          initial="hidden"
          animate="visible"
          exit="exit"
          variants={slideVariants}
          transition={{ type: "spring", stiffness: 300, damping: 30 }}
          className="relative flex flex-col h-full"
          ref={temporalResourceMonitorRef}
          style={{ height: "100%" }}
        >
          <div className="flex">
            <div
              style={{
                width: listWidth,
                height: headerHeight,
              }}
              className="flex-shrink-0 bg-background border-b border-r border-gray-200 dark:border-gray-700 flex items-center justify-center"
            >
              <span className="text-xs font-medium text-gray-500 dark:text-gray-400">
                Temporal Resources
              </span>
            </div>
            <TemporalResourceMonitorSetting />
          </div>
          {isDisplayed && (
            <>
              <div
                className="flex flex-1 overflow-y-auto overflow-x-hidden"
              >
                <div
                  className="relative m-0 flex overflow-hidden p-0 outline-0"
                  css={css`
                    width: 100%;
                  `}
                >
                  <div
                    css={css`
                      min-width: ${listWidth}px;
                      max-width: ${listWidth}px;
                    `}
                  >
                    <TemporalResourceTable
                      resources={temporalResource}
                      listWidth={listWidth}
                      rowHeight={rowHeight}
                      headerHeight={headerHeight}
                      contentHeight={temporalHeight - headerHeight - 20}
                      gripAreaWidth={gripAreaWidth}
                      scrollX={tableScrollX}
                      scrollY={scrollY}
                      listColumns={listColumns}
                      changeColumnWidth={changeColumnWidth}
                    />
                  </div>

                  <HorizontalResizer {...horizontalResizerProps} />

                  <div
                    className="hidden-scrollbar m-0 overflow-x-scroll p-0"
                    ref={gridRef}
                    dir="ltr"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width={svgWidth}
                      height={calendarProps.headerHeight}
                    >
                      <Calendar
                        {...calendarProps}
                        columnVirtualizer={columnVirtualizer}
                      />
                    </svg>
                    <TemporalResourceGrid
                      dates={calendarProps.dateSetup.dates}
                      resources={temporalResource}
                      svgWidth={svgWidth}
                      rowHeight={rowHeight}
                      columnWidth={columnWidth}
                      contentHeight={temporalHeight - headerHeight - 20}
                      todayColor={todayColor}
                      scrollY={scrollY}
                      columnVirtualizer={columnVirtualizer}
                    />
                  </div>
                </div>

                <div className="flex">
                  <HorizontalScroll
                    svgWidth={tableWidth}
                    minWidth={listWidth}
                    scroll={tableScrollX}
                    onScroll={handleTableScrollX}
                  />
                  <HorizontalScroll
                    svgWidth={svgWidth}
                    scroll={scrollX}
                    onScroll={handleScrollX}
                  />
                </div>
              </div>
            </>
          )}
          <VerticalResizer
            size={temporalHeight}
            handleChangeSize={handleVerticalResize}
            // reversed
          />
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default TemporalResourceMonitor;
