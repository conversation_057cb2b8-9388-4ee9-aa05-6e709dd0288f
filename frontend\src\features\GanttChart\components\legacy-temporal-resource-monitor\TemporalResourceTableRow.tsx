import { css } from "@emotion/react";

import { DragHandleProps, TemporalResource } from "@/common/types";
import { SVGGripDotsOutline } from "@/common/components/Svgs";
import { cn } from "@/common/helpers/tailwindMerge";

import { TemporalResourceColumn } from "../../hooks/useTemporalResourceListColumns";

export interface TemporalResourceTableRowProps {
  /** 何行目か */
  index: number;
  /** 表示する期間付きリソース */
  resource: TemporalResource;
  /** 表の表示幅 */
  listWidth: number;
  /** 行の高さ */
  rowHeight: number;
  /** Tableの並び替えを行うエリアの幅 */
  gripAreaWidth: number;
  /** 表に表示する項目の情報 */
  listColumns: TemporalResourceColumn[];
  /** ドラッグ中か否か */
  isDragging?: boolean;
  /** dndのdrag handleに渡すprops */
  dragHandleProps?: DragHandleProps;
}

/**
 * TemporalResourceTableの一行
 */
const TemporalResourceTableRow: React.FC<TemporalResourceTableRowProps> = ({
  index,
  resource,
  listWidth,
  rowHeight,
  gripAreaWidth,
  listColumns,
  isDragging,
  dragHandleProps: { setActivatorNodeRef, attributes, listeners } = {},
}) => {
  return (
    <div
      className={cn(
        "overflow-hidden bg-white",
        (index + 1) % 2 === 0 && "bg-gray-100",
      )}
      css={{
        maxWidth: listWidth,
      }}
    >
      <div
        css={css`
          height: ${rowHeight}px;
          max-width: ${listWidth}px;
          min-width: ${listWidth}px;
        `}
        className="table-row text-ellipsis"
      >
        <div
          className={cn(
            "whitespace-no-wrap table-cell cursor-grab overflow-hidden text-ellipsis align-middle",
            isDragging && "cursor-grabbing",
          )}
          css={css`
            min-width: ${gripAreaWidth}px;
            max-width: ${gripAreaWidth}px;
            min-height: ${rowHeight}px;
            max-height: ${rowHeight}px;
          `}
          ref={setActivatorNodeRef}
          {...attributes}
          {...listeners}
        >
          <SVGGripDotsOutline className="h-6 w-6 stroke-gray-800" />
        </div>
        {listColumns.map((col) => (
          <div
            key={col.name}
            css={css`
              min-width: ${col.width}px;
              max-width: ${col.width}px;
            `}
            className="whitespace-no-wrap table-cell overflow-hidden text-ellipsis align-middle"
          >
            <div className="pl-4">{resource[col.name]}</div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default TemporalResourceTableRow;
