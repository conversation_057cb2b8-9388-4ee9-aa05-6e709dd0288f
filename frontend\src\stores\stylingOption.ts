//src/stores/stylingOption.ts
import { create } from "zustand";
import { persist } from "zustand/middleware";

import { ViewMode } from "@/common/types";

import { StylingOption } from "@/features/GanttChart/types";

import createSelectors from "./utils/selectors";
import middlewares from "./utils/middlewares";

export const graphStyles = ["bar", "circle"] as const;
type GraphStyle = (typeof graphStyles)[number];

/**
 * リソースモニターのスタイル
 */
export interface ResourceMonitorStylingOption {
  graphStyle: GraphStyle;
}

/**
 * カレンダーのスタイル
 */
export interface CalendarStylingOption {
  /** 項目の幅 */
  columnWidth: {
    hour: number;
    quarterDay: number;
    halfDay: number;
    day: number;
    week: number;
    month: number;
    year: number;
  };
}

export interface StylingOptionState {
  /** Ganttコンポーネントとカレンダーのスタイルのタイプ */
  type: "small" | "medium" | "large" | "custom";
  /** Ganttコンポーネントのスタイル */
  gantt: StylingOption;
  /** カレンダーのスタイル */
  calendar: CalendarStylingOption;
  /** リソースモニターのスタイル */
  resourceMonitor: ResourceMonitorStylingOption;
  /** カレンダーの表示幅 */
  viewMode: ViewMode;
  /** リソースモニターの表示/非表示 */
  showResourceMonitor: boolean;
  /** 期間リソースモニターの表示/非表示 */
  showTemporalResourceMonitor: boolean;
  showInazumaLine: boolean; 
  /** ビューモードが変更されたかどうかを追跡するフラグ */
  isViewModeChanging: boolean;
  /** Auto scheduling処理中のアニメーション用フラグ */
  isAutoScheduling: boolean;
  /**
   * スタイルのtypeを変更する関数（カスタム以外）
   * @param type - custom以外のtype
   */
  changeTypePreset: (type: "small" | "medium" | "large") => void;
  /**
   * カスタムtypeに変更する関数
   * @param gantt - Ganttコンポーネントのスタイル
   * @param calendar - カレンダーのスタイル
   */
  changeCustomType: (
    gantt: StylingOption,
    calendar: CalendarStylingOption,
  ) => void;
  /**
   * Ganttコンポーネントのスタイルを更新する関数
   * @param option - Ganttコンポーネントのスタイル
   */
  updateGanttStylingOption: (option: StylingOption) => void;
  /**
   * カレンダーのスタイルを更新する関数
   * @param option - カレンダーのスタイル
   */
  updateCalendarStylingOption: (option: CalendarStylingOption) => void;
  /**
   * リソースモニターのグラフ形式を変更する関数
   * @param graphStyle - リソースモニターのグラフ形式
   */
  updateGraphStyle: (graphStyle: GraphStyle) => void;
  /**
   * ガントチャートの表示するレンジを更新する関数。
   * @param view - ガントチャートの表示するレンジ
   */
  changeViewMode: (view: ViewMode) => void;
  /**
   * リソースモニターの表示/非表示を切り替える関数
   */
  toggleResourceMonitor: () => void;
  /**
   * 期間リソースモニターの表示/非表示を切り替える関数
   */
  toggleTemporalResourceMonitor: () => void;
  toggleInazumaLine: () => void;
  /**
   * ビューモード変更アニメーション完了時に呼び出す関数
   */
  viewModeChangeComplete: () => void;
  /**
   * Auto scheduling処理を開始する時に呼び出す関数
   */
  startAutoScheduling: () => void;
  /**
   * Auto scheduling処理が完了した時に呼び出す関数
   */
  completeAutoScheduling: () => void;
}

const useStylingOptionBase = create<StylingOptionState>()(
  middlewares(
    persist(
      (set) => ({
        type: "medium",
        gantt: {
          headerHeight: 60,
          rowHeight: 50,
        },
        calendar: {
          columnWidth: {
            hour: 65,
            quarterDay: 65,
            halfDay: 65,
            day: 65,
            week: 250,
            month: 300,
            year: 350,
          },
        },
        resourceMonitor: {
          graphStyle: "bar",
        },
        viewMode: ViewMode.QuarterDay,
        showResourceMonitor: false,
        showTemporalResourceMonitor: false,
        showInazumaLine: false,
        isViewModeChanging: false,
        isAutoScheduling: false,
        changeTypePreset: (type) =>
          set((state) => {
            state.type = type;
            switch (type) {
              case "small":
                state.gantt = {
                  ...state.gantt,
                  headerHeight: 40,
                  rowHeight: 30,
                };
                state.calendar = {
                  ...state.calendar,
                  columnWidth: {
                    hour: 45,
                    quarterDay: 45,
                    halfDay: 45,
                    day: 45,
                    week: 150,
                    month: 200,
                    year: 250,
                  },
                };
                break;
              case "medium":
                state.gantt = {
                  ...state.gantt,
                  headerHeight: 60,
                  rowHeight: 50,
                };
                state.calendar = {
                  ...state.calendar,
                  columnWidth: {
                    hour: 65,
                    quarterDay: 65,
                    halfDay: 65,
                    day: 65,
                    week: 250,
                    month: 300,
                    year: 350,
                  },
                };
                break;
              case "large":
                state.gantt = {
                  ...state.gantt,
                  headerHeight: 80,
                  rowHeight: 70,
                };
                state.calendar = {
                  ...state.calendar,
                  columnWidth: {
                    hour: 85,
                    quarterDay: 85,
                    halfDay: 85,
                    day: 85,
                    week: 300,
                    month: 350,
                    year: 400,
                  },
                };
                break;
            }
          }),
        changeCustomType: (gantt, calendar) =>
          set((state) => {
            state.type = "custom";
            state.gantt = gantt;
            state.calendar = calendar;
          }),
        updateGanttStylingOption: (option) =>
          set((state) => {
            state.gantt = option;
          }),
        updateCalendarStylingOption: (option) =>
          set((state) => {
            state.calendar = option;
          }),
        updateGraphStyle: (graphStyle) =>
          set((state) => {
            state.resourceMonitor.graphStyle = graphStyle;
          }),
        changeViewMode: (viewMode) =>
          set((state) => {
            // Only set changing flag if viewMode is actually changing
            const isChanging = state.viewMode !== viewMode;
            
            return {
              viewMode: viewMode,
              isViewModeChanging: isChanging,
            };
          }),
        toggleResourceMonitor: () =>
          set((state) => ({
            showResourceMonitor: !state.showResourceMonitor,
          })),
        toggleTemporalResourceMonitor: () =>
          set((state) => ({
            showTemporalResourceMonitor: !state.showTemporalResourceMonitor,
          })),
        toggleInazumaLine: () =>
          set((state) => ({
            showInazumaLine: !state.showInazumaLine,
          })),
        viewModeChangeComplete: () => set({ isViewModeChanging: false }),
        startAutoScheduling: () => set({ isAutoScheduling: true }),
        completeAutoScheduling: () => set({ isAutoScheduling: false }),
      }),
      { name: "gantt-style-option" },
    ),
    { storeName: "stylingOption" },
  ),
);

export const useStylingOptionStore = createSelectors(useStylingOptionBase);
