// src/features/GanttChart/components/task-list/TaskListTable.tsx
import React, { useMemo, useEffect, useState, useCallback } from "react";
import {
  useSensors,
  useSensor,
  PointerSensor,
  DndContext,
  closestCenter,
  DragOverlay,
} from "@dnd-kit/core";
import {
  SortableContext,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable";

import { Column } from "@/stores/taskLists";
import { useGanttSchedulerStore } from "@/stores/ganttScheduler";

import { getCurrentDepth } from "@/common/helpers/depth-helper";
import { Task } from "@/common/types";
import { isUndefined } from "@/common/helpers/typeGuard";

import { BarSpan } from "@/features/GanttChart/types/barSpan";

import TaskListTableRow from "./TaskListTableRow";
import TaskListCells from "./TaskListCells";
import { Virtualizer } from "@tanstack/react-virtual";

let localeDateStringCache: { [key: string]: string } = {};

const toLocaleDateStringFactory =
  (locale: string) =>
  (date: Date, dateTimeOptions: Intl.DateTimeFormatOptions) => {
    const key = date.toString();
    let lds = localeDateStringCache[key];
    if (!lds) {
      lds = date.toLocaleString(locale, dateTimeOptions);
      localeDateStringCache[key] = lds;
    }
    return lds;
  };

export interface TaskListTableProps {
  tasks: BarSpan[];
  allTasks?: BarSpan[]; // Complete task list for expander logic
  displayColumns: Column[];
  rowHeight: number;
  taskListWidth: number;
  gripAreaWidth: number;
  fontFamily: string;
  fontSize: string;
  locale: string;
  selectedTaskId: string;
  disabled?: boolean;
  onExpanderClick: (task: Task) => void;
  handleFixedChange: (task: Task) => void;
  handleDoubleClickTaskName: (taskId: string) => void;
  rowVirtualizer?: Virtualizer<HTMLDivElement, Element>;

  /** Additional props for multi‐select */
  selectedTaskIds?: string[];
  onMultiSelect?: (tasks: Task[]) => void;
}

const TaskListTable: React.FC<TaskListTableProps> = (props) => {
  const {
  tasks,
  allTasks,
  displayColumns,
  rowHeight,
  taskListWidth,
  gripAreaWidth,
  fontFamily,
  fontSize,
  locale,
  disabled,
  onExpanderClick,
  handleFixedChange,
  handleDoubleClickTaskName,
  rowVirtualizer,
  selectedTaskIds = [],
  onMultiSelect,
  } = props;
  const timeUnit = useGanttSchedulerStore.use.timeUnit();
  const insertTask = useGanttSchedulerStore.use.insertTask();
  const [activeTask, setActiveTask] = useState<BarSpan | null>(null);

  const sensors = useSensors(useSensor(PointerSensor));

  let dateTimeOptions: Intl.DateTimeFormatOptions;
  switch (timeUnit) {
    case "daybyday":
    case "hours":
      dateTimeOptions = {
        weekday: "short",
        year: "numeric",
        month: "numeric",
        day: "numeric",
        hour12: false,
        hour: "numeric",
        minute: "numeric",
      };
      break;
    case "days":
    default:
      dateTimeOptions = {
        weekday: "short",
        year: "numeric",
        month: "numeric",
        day: "numeric",
      };
      break;
  }

  useEffect(() => {
    localeDateStringCache = {};
  }, [dateTimeOptions]);

  const toLocaleDateString = useMemo(
    () => toLocaleDateStringFactory(locale),
    [locale, dateTimeOptions],
  );

  // Memoize DnD handlers
  const handleDragStart = useCallback((event: any) => {
            if (activeTask?.id === event.active.id) return;
            const newTask = tasks.find((t) => t.id === event.active.id);
            setActiveTask(newTask ?? null);
  }, [activeTask, tasks]);
  const handleDragCancel = useCallback(() => {
            setActiveTask(null);
  }, []);
  const handleDragEnd = useCallback((event: any) => {
            const { active, over } = event;
            if (over && active.id !== over.id) {
              const activeIndex = tasks.findIndex((t) => t.id === active.id);
              const overIndex = tasks.findIndex((t) => t.id === over.id);
              if (activeIndex !== -1 && overIndex !== -1) {
                const beforeIndex =
                  activeIndex > overIndex ? overIndex - 1 : overIndex;
                const beforeId =
                  beforeIndex >= 0 && beforeIndex < tasks.length
                    ? tasks[beforeIndex].id
                    : undefined;
                insertTask(active.id, over.id, beforeId);
              }
            }
            setActiveTask(null);
  }, [tasks, insertTask]);

  return (
    <>
      <div
        className="table w-full border-b border-l"
        style={{
          fontFamily: fontFamily,
          fontSize: fontSize,
          maxWidth: taskListWidth,
        }}
      >
        <DndContext
          sensors={sensors}
          collisionDetection={closestCenter}
          onDragStart={handleDragStart}
          onDragCancel={handleDragCancel}
          onDragEnd={handleDragEnd}
        >
          <SortableContext items={tasks} strategy={verticalListSortingStrategy}>
            {rowVirtualizer ? (
              <div
                style={{
                  height: rowVirtualizer.getTotalSize(),
                  width: "100%",
                  position: "relative",
                }}
              >
                {rowVirtualizer.getVirtualItems().map((virtualItem) => {
                  if (virtualItem.index >= tasks.length) {
                    // empty row placeholder
                    return (
                      <div
                        key={`empty-${virtualItem.index}`}
                        style={{
                          position: "absolute",
                          top: 0,
                          left: 0,
                          width: "100%",
                          transform: `translateY(${virtualItem.start}px)`,
                        }}
                      >
                        <div
                          className="table-row"
                          style={{
                            height: rowHeight,
                            backgroundColor: "#f9f9f9",
                          }}
                        >
                          {/* placeholder */}
                        </div>
                      </div>
                    );
                  }
                  const t = tasks[virtualItem.index];
                  const depth = getCurrentDepth(t, tasks);
                  return (
                    <div
                      key={t.id}
                      style={{
                        position: "absolute",
                        top: 0,
                        left: 0,
                        width: "100%",
                        transform: `translateY(${virtualItem.start}px)`,
                      }}
                    >
                      <TaskListTableRow
                        index={virtualItem.index}
                        task={t}
                        depth={depth}
                        displayColumns={displayColumns}
                        rowHeight={rowHeight}
                        gripAreaWidth={gripAreaWidth}
                        disabled={disabled}
                        toLocaleDateString={toLocaleDateString}
                        onExpanderClick={onExpanderClick}
                        handleFixedChange={handleFixedChange}
                        handleDoubleClickTaskName={handleDoubleClickTaskName}
                        selectedTaskIds={selectedTaskIds}
                        onMultiSelect={onMultiSelect}
                        allTasks={allTasks || tasks}
                      />
                    </div>
                  );
                })}
              </div>
            ) : (
              tasks.map((t, index) => {
                const depth = getCurrentDepth(t, tasks);
                return (
                  <TaskListTableRow
                    key={t.id}
                    index={index}
                    task={t}
                    depth={depth}
                    displayColumns={displayColumns}
                    rowHeight={rowHeight}
                    gripAreaWidth={gripAreaWidth}
                    disabled={disabled}
                    toLocaleDateString={toLocaleDateString}
                    onExpanderClick={onExpanderClick}
                    handleFixedChange={handleFixedChange}
                    handleDoubleClickTaskName={handleDoubleClickTaskName}
                    selectedTaskIds={selectedTaskIds}
                    onMultiSelect={onMultiSelect}
                    allTasks={allTasks || tasks}
                  />
                );
              })
            )}
          </SortableContext>

          <DragOverlay>
            <div
              className="overflow-hidden whitespace-pre bg-white align-middle"
              style={{ width: taskListWidth, height: rowHeight }}
            >
              <div className="table-row">
                {activeTask && (
                  <TaskListCells
                    task={activeTask}
                    depth={getCurrentDepth(activeTask, tasks)}
                    displayColumns={displayColumns}
                    rowHeight={rowHeight}
                    gripAreaWidth={gripAreaWidth}
                    disabled={disabled}
                    isDragging={true}
                    toLocaleDateString={toLocaleDateString}
                    onExpanderClick={onExpanderClick}
                    handleFixedChange={handleFixedChange}
                    handleDoubleClickTaskName={handleDoubleClickTaskName}
                    selectedTaskIds={selectedTaskIds}
                    onMultiSelect={onMultiSelect}
                    allTasks={allTasks || tasks}
                  />
                )}
              </div>
            </div>
          </DragOverlay>
        </DndContext>
      </div>
    </>
  );
};

export default TaskListTable;
