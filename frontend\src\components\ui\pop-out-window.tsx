import { useEffect, useState } from 'react';
import { createPortal } from 'react-dom';
import { Button } from '@/components/ui/button';
import { ExternalLink } from 'lucide-react';
import { useMonitorStore, MonitorDisplayState } from '@/stores/monitorStore';

interface PopOutWindowProps {
  children: React.ReactNode;
  title: string;
  width?: number;
  height?: number;
  onClose?: () => void;
  features?: string[];
  className?: string;
  monitorType?: 'resource' | 'temporalResource';
  onPopOut?: () => void;
}

/**
 * Component that renders its children in a pop-out window
 * Uses window.open() and portals content into the new window
 */
const PopOutWindow = ({
  children,
  title,
  width = 800,
  height = 600,
  onClose,
  features = [],
  className = '',
  monitorType,
}: PopOutWindowProps) => {
  const [externalWindow, setExternalWindow] = useState<Window | null>(null);
  
  useEffect(() => {
    console.log('[PopOutWindow] Attempting to open popup window:', title);
    
    // Default features
    const defaultFeatures = [
      'popup=true',
      'menubar=no',
      'toolbar=no',
      'location=no',
      'status=no',
      `width=${width}`,
      `height=${height}`,
      'resizable=yes',
      'scrollbars=yes',
    ];
    
    // Combine with custom features
    const windowFeatures = [...defaultFeatures, ...features].join(',');
    
    console.log('[PopOutWindow] Window features:', windowFeatures);
    
    // Open the new window
    const newWindow = window.open('', title, windowFeatures);
    
    if (newWindow && !newWindow.closed) {
      console.log('[PopOutWindow] Successfully opened popup window');
      
      // Set basic HTML structure for the new window
      newWindow.document.title = title;
      
      // Copy over CSS from main window
      const mainStylesheets = document.querySelectorAll('link[rel="stylesheet"]');
      mainStylesheets.forEach(stylesheet => {
        const newStylesheet = newWindow.document.createElement('link');
        newStylesheet.rel = 'stylesheet';
        newStylesheet.href = (stylesheet as HTMLLinkElement).href;
        newWindow.document.head.appendChild(newStylesheet);
      });
      
      // Add custom styling
      const style = newWindow.document.createElement('style');
      style.textContent = `
        body {
          margin: 0;
          padding: 0;
          font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif;
          background-color: var(--background);
          color: var(--foreground);
          overflow: hidden;
        }
        #root {
          height: 100vh;
          overflow: hidden;
          position: relative;
        }
        .pop-out-close-button {
          position: absolute;
          top: 8px;
          right: 8px;
          z-index: 1000;
          background-color: var(--primary);
          color: var(--primary-foreground);
          border: none;
          border-radius: 4px;
          width: 28px;
          height: 28px;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          opacity: 0.7;
          transition: opacity 0.2s;
        }
        .pop-out-close-button:hover {
          opacity: 1;
        }
        .${className} {
          height: 100% !important;
          width: 100% !important;
          max-height: none !important;
        }
      `;
      newWindow.document.head.appendChild(style);
      
      // Create a container for our React content
      const container = newWindow.document.createElement('div');
      container.id = 'root';
      container.className = className;
      newWindow.document.body.appendChild(container);
      
      // Add close button
      const closeButton = newWindow.document.createElement('button');
      closeButton.className = 'pop-out-close-button';
      closeButton.title = 'Close window';
      closeButton.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="18" y1="6" x2="6" y2="18"></line><line x1="6" y1="6" x2="18" y2="18"></line></svg>';
      closeButton.onclick = () => {
        newWindow.close();
      };
      container.appendChild(closeButton);
      
      // Set window reference
      setExternalWindow(newWindow);
      
      // Sync theme if necessary
      if (document.documentElement.classList.contains('dark')) {
        newWindow.document.documentElement.classList.add('dark');
      }
      
      // Set up event listener for window close
      newWindow.addEventListener('beforeunload', () => {
        console.log('[PopOutWindow] Window is closing');
        if (onClose) onClose();
      });
      
      // Clean up when component unmounts
      return () => {
        if (!newWindow.closed) {
          newWindow.close();
        }
        if (onClose) onClose();
      };
    } else {
      // Window failed to open - handle the error
      console.error('[PopOutWindow] Failed to open popup window. This could be due to:', [
        '1. Popup blocker is enabled',
        '2. Browser security settings',
        '3. User denied popup permission',
        '4. Browser doesn\'t support window.open'
      ]);
      
      // Call onClose immediately to reset the state
      if (onClose) {
        console.log('[PopOutWindow] Calling onClose to reset state due to popup failure');
        onClose();
      }
    }
  }, [title, width, height, features, onClose, className]);
  
  // Portal our children into the external window when it's available
  return externalWindow ? createPortal(children, externalWindow.document.getElementById('root')!) : null;
};

/**
 * Button that opens content in a pop-out window when clicked
 */
export const PopOutButton = ({
  children,
  title,
  width,
  height,
  features,
  className,
  monitorType,
}: Omit<PopOutWindowProps, 'onClose'>) => {
  const [isOpen, setIsOpen] = useState(false);
  const [showError, setShowError] = useState(false);
  const setResourceMonitorState = useMonitorStore(state => state.setResourceMonitorState);
  const setTemporalResourceMonitorState = useMonitorStore(state => state.setTemporalResourceMonitorState);
  
  const handlePopOut = () => {
    console.log('[PopOutButton] Attempting to pop out monitor:', monitorType);
    setShowError(false);
    setIsOpen(true);
    
    if (monitorType === 'resource') {
      setResourceMonitorState('popped-out');
    } else if (monitorType === 'temporalResource') {
      setTemporalResourceMonitorState('popped-out');
    }
  };
  
  const handleClose = () => {
    console.log('[PopOutButton] Closing popup and resetting state for:', monitorType);
    setIsOpen(false);
    setShowError(false);
    
    if (monitorType === 'resource') {
      setResourceMonitorState('inline');
    } else if (monitorType === 'temporalResource') {
      setTemporalResourceMonitorState('inline');
    }
  };

  const handleError = () => {
    console.log('[PopOutButton] Popup failed, showing inline again for:', monitorType);
    setIsOpen(false);
    setShowError(true);
    
    // Reset to inline state if popup fails
    if (monitorType === 'resource') {
      setResourceMonitorState('inline');
    } else if (monitorType === 'temporalResource') {
      setTemporalResourceMonitorState('inline');
    }

    // Hide error message after 3 seconds
    setTimeout(() => setShowError(false), 3000);
  };
  
  return (
    <>
      <div className="relative">
        <Button
          variant="ghost"
          size="sm"
          className="p-1"
          onClick={handlePopOut}
          title="Pop out to separate window"
        >
          <ExternalLink className="h-4 w-4" />
        </Button>
        
        {showError && (
          <div className="absolute top-8 right-0 z-50 bg-destructive text-destructive-foreground text-xs p-2 rounded shadow-lg whitespace-nowrap">
            Popup blocked! Please allow popups for this site.
          </div>
        )}
      </div>
      
      {isOpen && (
        <PopOutWindow
          title={title}
          width={width}
          height={height}
          features={features}
          className={className}
          monitorType={monitorType}
          onClose={handleError}
        >
          {children}
        </PopOutWindow>
      )}
    </>
  );
};

export default PopOutWindow;
