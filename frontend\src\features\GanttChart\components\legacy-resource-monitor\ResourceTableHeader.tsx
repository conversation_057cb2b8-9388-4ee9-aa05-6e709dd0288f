import { convertResourcePropertyToJP } from "@/common/helpers/other-helper";
import { css } from "@emotion/react";
import { ResourceColumn } from "../../hooks/useResourceListColumns";
import HeaderColumnWidthResizer from "../task-list/HeaderColumnWidthResizer";
import { useEffect, useRef } from "react";

/**
 * {@link ResourceTableHeader}のprops
 */
export interface ResourceTableHeaderProps {
  /** ヘッダの高さ */
  headerHeight: number;
  /** Tableの並び替えを行うエリアの幅 */
  gripAreaWidth: number;
  /** 表示する項目情報 */
  resourceColumns: ResourceColumn[];
  /**
   * 項目の表示幅を変更する関数
   * @param name - 項目のプロパティ名
   * @param width - 新しい表示幅
   */
  changeColumnWidth: (name: string, width: number) => void;
  /** X方向のスクロール量 */
  scrollX: number;
}

/**
 * リソースモニターの左側の一覧表のヘッダ
 */
const ResourceTableHeader: React.FC<ResourceTableHeaderProps> = ({
  headerHeight,
  gripAreaWidth,
  resourceColumns,
  changeColumnWidth,
  scrollX,
}) => {
  const resourceTableHeaderRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (resourceTableHeaderRef.current !== null)
      resourceTableHeaderRef.current.scrollLeft = scrollX;
  }, [scrollX]);

  return (
    <div
      css={css`
        min-height: ${headerHeight}px;
      `}
      className="m-0 flex overflow-hidden border-y border-l border-solid border-gray-300 bg-white p-0 text-sm font-medium"
      ref={resourceTableHeaderRef}
    >
      <div
        css={css`
          margin-left: ${gripAreaWidth}px;
        `}
      ></div>
      {resourceColumns.map((col) => (
        <div
          key={col.name}
          css={css`
            min-width: ${col.width}px;
            max-width: ${col.width}px;
          `}
          className="flex items-center justify-between pl-4"
        >
          {convertResourcePropertyToJP(col.name)}
          <HeaderColumnWidthResizer
            width={11}
            columnName={col.name}
            columnWidth={col.width}
            headerHeight={headerHeight}
            changeColumnWidth={changeColumnWidth}
            changeColumnWidthGlobal={() => {}}
          />
        </div>
      ))}
    </div>
  );
};

export default ResourceTableHeader;
